{"name": "outlook-catchall-pricing-system", "version": "1.0.0", "description": "Outlook catchall system with subscription pricing and credit management", "type": "module", "main": "src/app.js", "scripts": {"start": "node src/app.js", "dev": "nodemon src/app.js", "migrate": "node scripts/migrate-pricing.js", "db:generate": "prisma generate", "db:push": "prisma db push", "db:migrate": "prisma migrate dev", "db:studio": "prisma studio", "db:reset": "prisma migrate reset", "cleanup": "node scripts/cleanup-expired.js", "test": "jest", "lint": "eslint src/", "format": "prettier --write src/"}, "dependencies": {"@prisma/client": "^5.7.0", "express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "dotenv": "^16.3.1", "axios": "^1.6.2", "node-cron": "^3.0.3", "uuid": "^9.0.1", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "express-rate-limit": "^7.1.5", "compression": "^1.7.4", "morgan": "^1.10.0"}, "devDependencies": {"prisma": "^5.7.0", "nodemon": "^3.0.2", "jest": "^29.7.0", "eslint": "^8.55.0", "prettier": "^3.1.0", "@types/node": "^20.10.4"}, "engines": {"node": ">=18.0.0"}, "keywords": ["outlook", "catchall", "email", "pricing", "subscription", "credits", "api"], "author": "Your Name", "license": "MIT"}