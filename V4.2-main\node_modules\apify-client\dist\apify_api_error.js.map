{"version": 3, "file": "apify_api_error.js", "sourceRoot": "", "sources": ["../src/apify_api_error.ts"], "names": [], "mappings": ";;;AAEA,+CAAyD;AACzD,mCAAmC;AAEnC;;;;;;;;GAQG;AACH,MAAM,mBAAmB,GAAG,8DAA8D,CAAC;AAE3F;;;;;;GAMG;AACH,MAAa,aAAc,SAAQ,KAAK;IA6CpC;;OAEG;IACH,YAAY,QAAuB,EAAE,OAAe;;QAChD,IAAI,OAAgB,CAAC;QACrB,IAAI,IAAwB,CAAC;QAC7B,IAAI,YAAY,GAAG,QAAQ,CAAC,IAAI,CAAC;QACjC,IAAI,SAA8C,CAAC;QAEnD,mGAAmG;QACnG,2DAA2D;QAC3D,IAAI,IAAA,gBAAQ,EAAC,YAAY,CAAC,EAAE,CAAC;YACzB,IAAI,CAAC;gBACD,YAAY,GAAG,IAAI,CAAC,KAAK,CAAC,IAAA,sCAAwB,EAAC,QAAQ,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC,CAAC;YAChF,CAAC;YAAC,MAAM,CAAC;gBACL,gEAAgE;YACpE,CAAC;QACL,CAAC;QAED,IAAI,YAAY,IAAI,YAAY,CAAC,KAAK,EAAE,CAAC;YACrC,MAAM,EAAE,KAAK,EAAE,GAAG,YAAY,CAAC;YAC/B,OAAO,GAAG,KAAK,CAAC,OAAO,CAAC;YACxB,IAAI,GAAG,KAAK,CAAC,IAAI,CAAC;YAClB,SAAS,GAAG,KAAK,CAAC,IAAI,CAAC;QAC3B,CAAC;aAAM,IAAI,YAAY,EAAE,CAAC;YACtB,IAAI,UAAU,CAAC;YACf,IAAI,CAAC;gBACD,UAAU,GAAG,IAAI,CAAC,SAAS,CAAC,YAAY,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;YACvD,CAAC;YAAC,MAAM,CAAC;gBACL,UAAU,GAAG,GAAG,YAAY,EAAE,CAAC;YACnC,CAAC;YACD,OAAO,GAAG,qBAAqB,UAAU,EAAE,CAAC;QAChD,CAAC;QACD,KAAK,CAAC,OAAO,CAAC,CAAC;QA7EV;;;;;WAAa;QAEtB;;;WAGG;QACH;;;;;WAAqB;QAErB;;WAEG;QACH;;;;;WAAmB;QAEnB;;WAEG;QACH;;;;;WAAc;QAEd;;WAEG;QACH;;;;;WAAgB;QAEhB;;WAEG;QACH;;;;;WAAoB;QAEpB;;WAEG;QACH;;;;;WAAc;QAEd;;;WAGG;QACH;;;;;WAAsB;QAEtB;;WAEG;QACH;;;;;WAA+B;QAqC3B,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC;QAClC,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,gCAAgC,EAAE,CAAC;QAC5D,IAAI,CAAC,UAAU,GAAG,QAAQ,CAAC,MAAM,CAAC;QAClC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;QACjB,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;QACvB,IAAI,CAAC,UAAU,GAAG,MAAA,QAAQ,CAAC,MAAM,0CAAE,MAAM,CAAC;QAC1C,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,4BAA4B,CAAC,QAAQ,CAAC,CAAC;QAExD,MAAM,KAAK,GAAG,IAAI,CAAC,KAAM,CAAC;QAE1B,IAAI,CAAC,aAAa,GAAG,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC;QACtD,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,eAAe,EAAE,CAAC;QAEpC,IAAI,CAAC,IAAI,GAAG,SAAS,CAAC;IAC1B,CAAC;IAEO,4BAA4B,CAAC,QAAuB;;QACxD,MAAM,SAAS,GAAG,MAAA,QAAQ,CAAC,MAAM,0CAAE,GAAG,CAAC;QACvC,IAAI,GAAG,CAAC;QACR,IAAI,CAAC;YACD,GAAG,GAAG,IAAI,GAAG,CAAC,SAAU,CAAC,CAAC;QAC9B,CAAC;QAAC,MAAM,CAAC;YACL,OAAO,SAAS,CAAC;QACrB,CAAC;QACD,OAAO,GAAG,CAAC,QAAQ,GAAG,GAAG,CAAC,MAAM,CAAC;IACrC,CAAC;IAEO,gCAAgC;QACpC,MAAM,KAAK,GAAG,IAAI,CAAC,KAAM,CAAC,KAAK,CAAC,mBAAmB,CAAC,CAAC;QACrD,IAAI,KAAK;YAAE,OAAO,GAAG,KAAK,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC;QAC5C,OAAO,SAAS,CAAC;IACrB,CAAC;IAED;;;;;;;;;;;;;OAaG;IACK,eAAe;QACnB,MAAM,EAAE,IAAI,EAAE,GAAG,KAAK,EAAE,GAAG,IAAI,CAAC;QAEhC,MAAM,KAAK,GAAG,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC;aAC9B,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE;YACZ,qDAAqD;YACrD,mDAAmD;YACnD,IAAI,CAAC,KAAK,eAAe;gBAAE,CAAC,GAAG,OAAO,CAAC;YACvC,OAAO,KAAK,CAAC,KAAK,CAAC,EAAE,CAAC;QAC1B,CAAC,CAAC;aACD,IAAI,CAAC,IAAI,CAAC,CAAC;QAEhB,OAAO,GAAG,IAAI,KAAK,IAAI,CAAC,OAAO,KAAK,KAAK,EAAE,CAAC;IAChD,CAAC;CACJ;AA7ID,sCA6IC"}