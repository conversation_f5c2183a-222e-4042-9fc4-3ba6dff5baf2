{"name": "@crawlee/core", "version": "3.13.9", "description": "The scalable web crawling and scraping library for JavaScript/Node.js. Enables development of data extraction and web automation jobs (not only) with headless Chrome and Puppeteer.", "engines": {"node": ">=16.0.0"}, "main": "./index.js", "module": "./index.mjs", "types": "./index.d.ts", "exports": {".": {"import": "./index.mjs", "require": "./index.js", "types": "./index.d.ts"}, "./package.json": "./package.json"}, "keywords": ["apify", "headless", "chrome", "puppeteer", "crawler", "scraper"], "author": {"name": "Apify", "email": "<EMAIL>", "url": "https://apify.com"}, "contributors": ["<PERSON> <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>"], "license": "Apache-2.0", "repository": {"type": "git", "url": "git+https://github.com/apify/crawlee"}, "bugs": {"url": "https://github.com/apify/crawlee/issues"}, "homepage": "https://crawlee.dev", "scripts": {"build": "yarn clean && yarn compile && yarn copy", "clean": "rimraf ./dist", "compile": "tsc -p tsconfig.build.json && gen-esm-wrapper ./index.js ./index.mjs", "copy": "tsx ../../scripts/copy.ts"}, "publishConfig": {"access": "public"}, "dependencies": {"@apify/consts": "^2.20.0", "@apify/datastructures": "^2.0.0", "@apify/log": "^2.4.0", "@apify/pseudo_url": "^2.0.30", "@apify/timeout": "^0.3.0", "@apify/utilities": "^2.7.10", "@crawlee/memory-storage": "3.13.9", "@crawlee/types": "3.13.9", "@crawlee/utils": "3.13.9", "@sapphire/async-queue": "^1.5.1", "@vladfrangu/async_event_emitter": "^2.2.2", "csv-stringify": "^6.2.0", "fs-extra": "^11.0.0", "got-scraping": "^4.0.0", "json5": "^2.2.3", "minimatch": "^9.0.0", "ow": "^0.28.1", "stream-json": "^1.8.0", "tldts": "^7.0.0", "tough-cookie": "^5.0.0", "tslib": "^2.4.0", "type-fest": "^4.0.0"}, "lerna": {"command": {"publish": {"assets": []}}}, "gitHead": "371eab1afca23ed0619cf7d32134b8c33d17dfe0"}