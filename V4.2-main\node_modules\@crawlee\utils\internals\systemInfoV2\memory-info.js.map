{"version": 3, "file": "memory-info.js", "sourceRoot": "", "sources": ["../../../src/internals/systemInfoV2/memory-info.ts"], "names": [], "mappings": ";;AAmDA,0CAgGC;;AAnJD,2DAA8C;AAC9C,+CAA4C;AAC5C,qCAA4C;AAE5C,6DAA6B;AAE7B,wCAAyD;AACzD,uCAAmC;AAEnC,MAAM,iBAAiB,GAAG;IACtB,KAAK,EAAE;QACH,EAAE,EAAE,6CAA6C;QACjD,EAAE,EAAE,2BAA2B;KAClC;IACD,IAAI,EAAE;QACF,EAAE,EAAE,6CAA6C;QACjD,EAAE,EAAE,+BAA+B;KACtC;CACJ,CAAC;AAsBF;;;;;;;;;;GAUG;AACI,KAAK,UAAU,eAAe,CAAC,aAAa,GAAG,KAAK;IACvD,IAAI,gBAAgB,GAAG,CAAC,CAAC,CAAC;IAC1B,IAAI,mBAAmB,GAAG,CAAC,CAAC;IAE5B,2DAA2D;IAC3D,oCAAoC;IACpC,IAAI,IAAA,kBAAQ,GAAE,EAAE,CAAC;QACb,oBAAoB;QACpB,gBAAgB,GAAG,OAAO,CAAC,WAAW,EAAE,CAAC,GAAG,CAAC;QAE7C,8CAA8C;QAC9C,MAAM,OAAO,GAAG,IAAA,6BAAQ,EAAC,mBAAmB,CAAC,CAAC,QAAQ,EAAE,CAAC;QACzD,MAAM,MAAM,GAAG,OAAO,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC,CAAC;QACnE,uFAAuF;QACvF,qGAAqG;QACrG,mBAAmB,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,GAAG,IAAI,GAAG,gBAAgB,CAAC;IAChE,CAAC;SAAM,CAAC;QACJ,sCAAsC;QACtC,MAAM,SAAS,GAAG,MAAM,IAAA,gBAAM,EAAC,OAAO,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC;QAElD,SAAS,CAAC,OAAO,CAAC,CAAC,GAAG,EAAE,EAAE;YACtB,yCAAyC;YACzC,IAAI,GAAG,CAAC,GAAG,KAAK,GAAG,OAAO,CAAC,GAAG,EAAE,EAAE,CAAC;gBAC/B,gBAAgB,GAAG,GAAG,CAAC,GAAG,CAAC;gBAC3B,OAAO;YACX,CAAC;YACD,mBAAmB,IAAI,GAAG,CAAC,GAAG,CAAC;QACnC,CAAC,CAAC,CAAC;IACP,CAAC;IAED,IAAI,UAAkB,CAAC;IACvB,IAAI,SAAiB,CAAC;IACtB,IAAI,SAAiB,CAAC;IAEtB,IAAI,IAAA,kBAAQ,GAAE,EAAE,CAAC;QACb,sCAAsC;QACtC,UAAU,GAAG,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,+BAAgC,EAAE,EAAE,CAAC,GAAG,OAAO,CAAC;QAClF,SAAS,GAAG,gBAAgB,GAAG,mBAAmB,CAAC;QACnD,SAAS,GAAG,UAAU,GAAG,SAAS,CAAC;QAEnC,aAAG,CAAC,KAAK,CAAC,kBAAkB,UAAU,SAAS,SAAS,aAAa,CAAC,CAAC;IAC3E,CAAC;SAAM,IAAI,aAAa,EAAE,CAAC;QACvB,+DAA+D;QAE/D,MAAM,cAAc,GAAG,MAAM,IAAA,2BAAiB,GAAE,CAAC;QAEjD,IAAI,CAAC;YACD,IAAI,cAAc,KAAK,IAAI,EAAE,CAAC;gBAC1B,MAAM,IAAI,KAAK,CAAC,sBAAsB,CAAC,CAAC;YAC5C,CAAC;YACD,IAAI,CAAC,aAAa,EAAE,YAAY,CAAC,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC;gBAClD,IAAA,mBAAQ,EAAC,iBAAiB,CAAC,KAAK,CAAC,cAAc,CAAC,EAAE,MAAM,CAAC;gBACzD,IAAA,mBAAQ,EAAC,iBAAiB,CAAC,IAAI,CAAC,cAAc,CAAC,EAAE,MAAM,CAAC;aAC3D,CAAC,CAAC;YAEH,gHAAgH;YAChH,aAAa,GAAG,aAAa,CAAC,OAAO,CAAC,gBAAgB,EAAE,EAAE,CAAC,CAAC;YAC5D,YAAY,GAAG,YAAY,CAAC,OAAO,CAAC,gBAAgB,EAAE,EAAE,CAAC,CAAC;YAE1D,4DAA4D;YAC5D,oIAAoI;YACpI,IAAI,aAAa,KAAK,KAAK,EAAE,CAAC;gBAC1B,UAAU,GAAG,IAAA,kBAAQ,GAAE,CAAC;gBACxB,yFAAyF;gBACzF,8CAA8C;YAClD,CAAC;iBAAM,CAAC;gBACJ,UAAU,GAAG,QAAQ,CAAC,aAAa,EAAE,EAAE,CAAC,CAAC;gBACzC,MAAM,gCAAgC,GAAG,UAAU,GAAG,MAAM,CAAC,gBAAgB,CAAC;gBAC9E,IAAI,gCAAgC;oBAAE,UAAU,GAAG,IAAA,kBAAQ,GAAE,CAAC;YAClE,CAAC;YACD,SAAS,GAAG,QAAQ,CAAC,YAAY,EAAE,EAAE,CAAC,CAAC;YACvC,SAAS,GAAG,UAAU,GAAG,SAAS,CAAC;QACvC,CAAC;QAAC,OAAO,GAAG,EAAE,CAAC;YACX,0CAA0C;YAC1C,aAAG,CAAC,UAAU,CACV,sFAAsF;gBAClF,mGAAmG;gBACnG,UAAW,GAAa,CAAC,OAAO,EAAE,CACzC,CAAC;YACF,UAAU,GAAG,IAAA,kBAAQ,GAAE,CAAC;YACxB,SAAS,GAAG,IAAA,iBAAO,GAAE,CAAC;YACtB,SAAS,GAAG,UAAU,GAAG,SAAS,CAAC;QACvC,CAAC;IACL,CAAC;SAAM,CAAC;QACJ,UAAU,GAAG,IAAA,kBAAQ,GAAE,CAAC;QACxB,SAAS,GAAG,IAAA,iBAAO,GAAE,CAAC;QACtB,SAAS,GAAG,UAAU,GAAG,SAAS,CAAC;IACvC,CAAC;IAED,OAAO;QACH,UAAU;QACV,SAAS;QACT,SAAS;QACT,gBAAgB;QAChB,mBAAmB;KACtB,CAAC;AACN,CAAC"}