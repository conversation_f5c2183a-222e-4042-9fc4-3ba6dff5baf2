import { EventManager } from '@crawlee/core';
import { Configuration } from './configuration.js';
/**
 * Gets an instance of a Node.js'
 * [EventEmitter](https://nodejs.org/api/events.html#events_class_eventemitter)
 * class that emits various events from the SDK or the Apify platform.
 * The event emitter is initialized by calling the {@link Actor.main} function.
 *
 * **Example usage:**
 *
 * ```javascript
 * Actor.on('cpuInfo', (data) => {
 *   if (data.isCpuOverloaded) console.log('Oh no, the CPU is overloaded!');
 * });
 * ```
 *
 * The following events are emitted:
 *
 * - `cpuInfo`: `{ "isCpuOverloaded": Boolean }`
 *   The event is emitted approximately every second
 *   and it indicates whether the Actor is using the maximum of available CPU resources.
 *   If that's the case, the Actor should not add more workload.
 *   For example, this event is used by the {@link AutoscaledPool} class.
 * - `migrating`: `void`
 *   Emitted when the Actor running on the Apify platform is going to be migrated to another worker server soon.
 *   You can use it to persist the state of the Actor and gracefully stop your in-progress tasks,
 *   so that they are not interrupted by the migration.
 *   For example, this is used by the {@link RequestList} class.
 * - `aborting`: `void`
 *   When a user aborts an Actor run on the Apify platform, they can choose to abort gracefully to allow
 *   the Actor some time before getting killed. This graceful abort emits the `aborting` event which the SDK
 *   uses to gracefully stop running crawls and you can use it to do your own cleanup as well.
 * - `persistState`: `{ "isMigrating": Boolean }`
 *   Emitted in regular intervals (by default 60 seconds) to notify all components of Apify SDK that it is time to persist
 *   their state, in order to avoid repeating all work when the Actor restarts.
 *   This event is automatically emitted together with the `migrating` event,
 *   in which case the `isMigrating` flag is set to `true`. Otherwise the flag is `false`.
 *   Note that the `persistState` event is provided merely for user convenience,
 *   you can achieve the same effect using `setInterval()` and listening for the `migrating` event.
 */
export declare class PlatformEventManager extends EventManager {
    readonly config: Configuration;
    /** Websocket connection to Actor events. */
    private eventsWs?;
    constructor(config?: Configuration);
    /**
     * Initializes `Actor.events` event emitter by creating a connection to a websocket that provides them.
     * This is an internal function that is automatically called by `Actor.main()`.
     */
    init(): Promise<void>;
    private createWebSocketConnection;
    /**
     * Closes websocket providing events from Actor infrastructure and also stops sending internal events
     * of Apify package such as `persistState`.
     * This is automatically called at the end of `Actor.main()`.
     */
    close(): Promise<void>;
}
//# sourceMappingURL=platform_event_manager.d.ts.map