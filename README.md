# Outlook Catchall Pricing System

A comprehensive subscription-based pricing system for Outlook catchall email processing with credit management, automatic expiration, and multi-endpoint support.

## 🚀 Features

- **Updated Pricing**: $20 per 10K credits per month (down from $30)
- **Credit Expiration**: Credits expire after 1 month
- **Subscription Management**: Full subscription lifecycle management
- **Credit Tracking**: Detailed usage and transaction history
- **Automatic Cleanup**: Daily cleanup of expired subscriptions
- **Multi-endpoint Support**: Load balancing across multiple API endpoints
- **Rate Limiting**: Built-in rate limiting and security
- **Prisma Integration**: Robust database management with conflict resolution

## 📋 Prerequisites

- Node.js 18+ 
- PostgreSQL (or MySQL/SQLite)
- npm or yarn

## 🛠️ Installation

1. **Clone and setup**:
```bash
git clone <repository-url>
cd outlook-catchall-pricing-system
npm install
```

2. **Environment setup**:
```bash
cp .env.example .env
# Edit .env with your database and API configurations
```

3. **Database setup**:
```bash
# Generate Prisma client
npm run db:generate

# Push schema to database
npm run db:push

# Or run migrations
npm run db:migrate
```

4. **Run migration to resolve Prisma conflicts and update pricing**:
```bash
npm run migrate
```

## 🚀 Usage

### Start the server
```bash
# Development
npm run dev

# Production
npm start
```

### API Endpoints

#### Get User Credits
```bash
GET /api/pricing/users/:userId/credits
```

#### Create Subscription
```bash
POST /api/pricing/subscriptions
{
  "userId": "user123",
  "planId": "plan456",
  "creditsAmount": 10000
}
```

#### Use Credits
```bash
POST /api/pricing/credits/use
{
  "userId": "user123",
  "service": "email_verification",
  "quantity": 100,
  "creditsPerItem": 1
}
```

#### Check Credits
```bash
POST /api/pricing/credits/check
{
  "userId": "user123",
  "creditsNeeded": 500
}
```

## 🔧 Resolving Prisma Conflicts

The migration script automatically handles common Prisma conflicts:

1. **Connection conflicts**: Resets and reconnects Prisma client
2. **Index conflicts**: Drops and recreates conflicting indexes
3. **Schema conflicts**: Updates schema to new pricing structure
4. **Data migration**: Migrates existing users to new pricing

### Manual Conflict Resolution

If you encounter specific Prisma conflicts:

```bash
# Reset database (WARNING: This will delete all data)
npm run db:reset

# Or manually resolve conflicts
npx prisma db push --force-reset
```

## 💰 Pricing Structure

### New Pricing (Updated)
- **Price**: $20 per 10,000 credits
- **Billing**: Monthly
- **Expiration**: Credits expire after 30 days
- **Minimum**: 10,000 credits per purchase

### Credit Usage
- Email verification: 1 credit per email
- Outlook processing: 1 credit per email
- Bulk operations: Configurable credits per item

## 🔄 Credit Management

### Automatic Expiration
- Credits expire 30 days after purchase
- Daily cleanup job removes expired credits
- Users receive notifications before expiration

### Usage Tracking
- Real-time credit balance updates
- Detailed transaction history
- Usage analytics and reporting

## 🛡️ Security Features

- Rate limiting (100 requests per 15 minutes)
- CORS protection
- Helmet security headers
- Input validation and sanitization
- Error handling and logging

## 📊 Monitoring

### Health Check
```bash
GET /health
```

### Cleanup Job
Runs daily at 2 AM to clean up expired subscriptions:
```bash
# Manual cleanup
npm run cleanup
```

## 🧪 Testing

```bash
# Run tests
npm test

# Run with coverage
npm run test:coverage
```

## 📝 Environment Variables

Key environment variables (see `.env.example`):

- `DATABASE_URL`: PostgreSQL connection string
- `PORT`: Server port (default: 3000)
- `DEFAULT_PRICE_PER_10K`: Price per 10K credits (20.00)
- `CREDIT_EXPIRY_DAYS`: Credit expiration period (30)
- `OUTLOOK_API_ENDPOINTS`: Comma-separated API endpoints

## 🔧 Troubleshooting

### Common Issues

1. **Prisma Connection Error**:
```bash
npx prisma generate
npm run db:push
```

2. **Migration Conflicts**:
```bash
npm run migrate
```

3. **Port Already in Use**:
```bash
# Change PORT in .env file
PORT=3001
```

### Database Issues

1. **Reset Database**:
```bash
npm run db:reset
```

2. **View Database**:
```bash
npm run db:studio
```

## 📈 Scaling

### Production Deployment
- Use environment variables for configuration
- Set up database connection pooling
- Configure load balancing for multiple instances
- Set up monitoring and logging
- Use PM2 or similar for process management

### Performance Optimization
- Database indexing on frequently queried fields
- Caching for pricing plans and user data
- Background job processing for cleanup tasks
- API response compression

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## 📄 License

MIT License - see LICENSE file for details

## 🆘 Support

For issues and questions:
1. Check the troubleshooting section
2. Review the API documentation
3. Create an issue in the repository
4. Contact support team

---

**Note**: This system resolves Prisma conflicts and updates pricing from $30 to $20 per 10K credits with 1-month expiration as requested.
