{"version": 3, "file": "key_value_store.d.ts", "sourceRoot": "", "sources": ["../../src/resource_clients/key_value_store.ts"], "names": [], "mappings": "AAAA,OAAO,KAAK,EAAE,QAAQ,EAAE,MAAM,aAAa,CAAC;AAG5C,OAAO,KAAK,EAAE,SAAS,EAAE,MAAM,WAAW,CAAC;AAE3C,OAAO,KAAK,EAAE,sBAAsB,EAAE,MAAM,eAAe,CAAC;AAI5D,OAAO,KAAK,EAAE,2BAA2B,EAAE,MAAM,oBAAoB,CAAC;AACtE,OAAO,EAGH,cAAc,EAEjB,MAAM,yBAAyB,CAAC;AAIjC,qBAAa,mBAAoB,SAAQ,cAAc;IACnD;;OAEG;gBACS,OAAO,EAAE,2BAA2B;IAOhD;;OAEG;IACG,GAAG,IAAI,OAAO,CAAC,aAAa,GAAG,SAAS,CAAC;IAI/C;;OAEG;IACG,MAAM,CAAC,SAAS,EAAE,2BAA2B,GAAG,OAAO,CAAC,aAAa,CAAC;IAM5E;;OAEG;IACG,MAAM,IAAI,OAAO,CAAC,IAAI,CAAC;IAI7B;;OAEG;IACG,QAAQ,CAAC,OAAO,GAAE,6BAAkC,GAAG,OAAO,CAAC,4BAA4B,CAAC;IAqBlG;;;;;;OAMG;IACG,YAAY,CAAC,GAAG,EAAE,MAAM,GAAG,OAAO,CAAC,OAAO,CAAC;IAiBjD;;;;;;;;OAQG;IACG,SAAS,CAAC,GAAG,EAAE,MAAM,GAAG,OAAO,CAAC,mBAAmB,CAAC,SAAS,CAAC,GAAG,SAAS,CAAC;IAE3E,SAAS,CAAC,OAAO,SAAS,8BAA8B,GAAG,8BAA8B,EAC3F,GAAG,EAAE,MAAM,EACX,OAAO,EAAE,OAAO,GACjB,OAAO,CAAC,mBAAmB,CAAC,qBAAqB,CAAC,OAAO,CAAC,CAAC,GAAG,SAAS,CAAC;IAmD3E;;;;;;;;;;OAUG;IACG,SAAS,CAAC,MAAM,EAAE,mBAAmB,CAAC,SAAS,CAAC,EAAE,OAAO,GAAE,0BAA+B,GAAG,OAAO,CAAC,IAAI,CAAC;IAqDhH;;OAEG;IACG,YAAY,CAAC,GAAG,EAAE,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC;CAUjD;AAED,MAAM,WAAW,aAAa;IAC1B,EAAE,EAAE,MAAM,CAAC;IACX,IAAI,CAAC,EAAE,MAAM,CAAC;IACd,KAAK,CAAC,EAAE,MAAM,CAAC;IACf,MAAM,EAAE,MAAM,CAAC;IACf,SAAS,EAAE,IAAI,CAAC;IAChB,UAAU,EAAE,IAAI,CAAC;IACjB,UAAU,EAAE,IAAI,CAAC;IACjB,KAAK,CAAC,EAAE,MAAM,CAAC;IACf,QAAQ,CAAC,EAAE,MAAM,CAAC;IAClB,KAAK,CAAC,EAAE,kBAAkB,CAAC;IAC3B,aAAa,CAAC,EAAE,sBAAsB,GAAG,IAAI,CAAC;CACjD;AAED,MAAM,WAAW,kBAAkB;IAC/B,SAAS,CAAC,EAAE,MAAM,CAAC;IACnB,UAAU,CAAC,EAAE,MAAM,CAAC;IACpB,WAAW,CAAC,EAAE,MAAM,CAAC;IACrB,SAAS,CAAC,EAAE,MAAM,CAAC;IACnB,YAAY,CAAC,EAAE,MAAM,CAAC;CACzB;AAED,MAAM,WAAW,2BAA2B;IACxC,IAAI,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;IACrB,KAAK,CAAC,EAAE,MAAM,CAAC;IACf,aAAa,CAAC,EAAE,sBAAsB,GAAG,IAAI,CAAC;CACjD;AAED,MAAM,WAAW,6BAA6B;IAC1C,KAAK,CAAC,EAAE,MAAM,CAAC;IACf,iBAAiB,CAAC,EAAE,MAAM,CAAC;IAC3B,UAAU,CAAC,EAAE,MAAM,CAAC;IACpB,MAAM,CAAC,EAAE,MAAM,CAAC;CACnB;AAED,MAAM,WAAW,4BAA4B;IACzC,KAAK,EAAE,MAAM,CAAC;IACd,KAAK,EAAE,MAAM,CAAC;IACd,iBAAiB,EAAE,MAAM,CAAC;IAC1B,WAAW,EAAE,OAAO,CAAC;IACrB,qBAAqB,EAAE,MAAM,CAAC;IAC9B,KAAK,EAAE,gBAAgB,EAAE,CAAC;CAC7B;AAED,MAAM,WAAW,gBAAgB;IAC7B,GAAG,EAAE,MAAM,CAAC;IACZ,IAAI,EAAE,MAAM,CAAC;CAChB;AAED,MAAM,WAAW,8BAA8B;IAC3C,MAAM,CAAC,EAAE,OAAO,CAAC;IACjB,MAAM,CAAC,EAAE,OAAO,CAAC;CACpB;AAED,MAAM,WAAW,mBAAmB,CAAC,CAAC;IAClC,GAAG,EAAE,MAAM,CAAC;IACZ,KAAK,EAAE,CAAC,CAAC;IACT,WAAW,CAAC,EAAE,MAAM,CAAC;CACxB;AAED,MAAM,WAAW,0BAA0B;IACvC,WAAW,CAAC,EAAE,MAAM,CAAC;IACrB,kBAAkB,CAAC,EAAE,OAAO,CAAC;CAChC;AAED,MAAM,MAAM,qBAAqB,CAAC,OAAO,SAAS,8BAA8B,IAAI,OAAO,CAAC,QAAQ,CAAC,SAAS,IAAI,GAC5G,QAAQ,GACR,OAAO,CAAC,QAAQ,CAAC,SAAS,IAAI,GAC5B,MAAM,GACN,SAAS,CAAC"}