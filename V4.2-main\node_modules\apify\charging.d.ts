import type { ActorRunPricingInfo, ApifyClient } from 'apify-client';
import type { Configuration } from './configuration.js';
export interface ChargeOptions {
    eventName: string;
    count?: number;
}
export interface ChargeResult {
    eventChargeLimitReached: boolean;
    chargedCount: number;
    chargeableWithinLimit: Record<string, number>;
}
export interface ActorPricingInfo {
    pricingModel?: ActorRunPricingInfo['pricingModel'];
    maxTotalChargeUsd: number;
    isPayPerEvent: boolean;
    perEventPrices: Record<string, number>;
}
/**
 * Handles pay-per-event charging.
 */
export declare class ChargingManager {
    private readonly LOCAL_CHARGING_LOG_DATASET_NAME;
    private readonly PLATFORM_CHARGING_LOG_DATASET_ID_KEY;
    private maxTotalChargeUsd;
    private isAtHome;
    private actorRunId?;
    private pricingModel?;
    private purgeChargingLogDataset;
    private useChargingLogDataset;
    private notPpeWarningPrinted;
    private pricingInfo;
    private chargingState?;
    private chargingLogDataset?;
    private apifyClient;
    constructor(configuration: Configuration, apifyClient: ApifyClient);
    private get isPayPerEvent();
    /**
     * Initialize the ChargingManager by loading pricing information and charging state via Apify API.
     */
    init(): Promise<void>;
    private ensureChargingLogDatasetOnPlatform;
    /**
     * Get information about the pricing for this Actor.
     */
    getPricingInfo(): ActorPricingInfo;
    /**
     * Charge for a specified number of events - sub-operations of the Actor.
     *
     * @param options The name of the event to charge for and the number of events to be charged.
     */
    charge({ eventName, count, }: ChargeOptions): Promise<ChargeResult>;
    /**
     * Get the number of events with given name that the Actor has charged for so far.
     */
    getChargedEventCount(eventName: string): number;
    /**
     * Get the maximum amount of money that the Actor is allowed to charge.
     */
    getMaxTotalChargeUsd(): number;
    private calculateTotalChargedAmount;
    /**
     * How many events of a given type can still be charged for before reaching the limit;
     * If the event is not registered, returns Infinity (free of charge)
     */
    calculateMaxEventChargeCountWithinLimit(eventName: string): number;
}
//# sourceMappingURL=charging.d.ts.map