import type { ConfigurationOptions, EventManager, EventTypeName, RecordOptions, UseStateOptions } from '@crawlee/core';
import { Dataset, RequestQueue } from '@crawlee/core';
import type { Awaitable, Dictionary, SetStatusMessageOptions, StorageClient } from '@crawlee/types';
import type { ActorCallOptions, ApifyClientOptions, RunAbortOptions, TaskCallOptions, Webhook, WebhookEventType } from 'apify-client';
import { ActorRun as ClientActorRun, ApifyClient } from 'apify-client';
import type { ChargeOptions, ChargeResult } from './charging.js';
import { ChargingManager } from './charging.js';
import { Configuration } from './configuration.js';
import { KeyValueStore } from './key_value_store.js';
import type { ProxyConfigurationOptions } from './proxy_configuration.js';
import { ProxyConfiguration } from './proxy_configuration.js';
export interface InitOptions {
    storage?: StorageClient;
}
export interface ExitOptions {
    /** Exit with given status message */
    statusMessage?: string;
    /**
     * Amount of time, in seconds, to wait for all event handlers to finish before exiting the process.
     * @default 30
     */
    timeoutSecs?: number;
    /** Exit code, defaults to 0 */
    exitCode?: number;
    /** Call `process.exit()`? Defaults to true */
    exit?: boolean;
}
export interface MainOptions extends ExitOptions, InitOptions {
}
/**
 * Parsed representation of the Apify environment variables.
 * This object is returned by the {@link Actor.getEnv} function.
 */
export interface ApifyEnv {
    /**
     * ID of the Actor (ACTOR_ID)
     */
    actorId: string | null;
    /**
     * ID of the Actor run (ACTOR_RUN_ID)
     */
    actorRunId: string | null;
    /**
     * ID of the Actor task (ACTOR_TASK_ID)
     */
    actorTaskId: string | null;
    /**
     * ID of the Actor build used in the run. (ACTOR_BUILD_ID)
     */
    actorBuildId: string | null;
    /**
     * ID of the user who started the Actor - note that it might be
     * different than the owner of the Actor (APIFY_USER_ID)
     */
    userId: string | null;
    /**
     * Authentication token representing privileges given to the Actor run,
     * it can be passed to various Apify APIs (APIFY_TOKEN)
     */
    token: string | null;
    /**
     * Date when the Actor was started (ACTOR_STARTED_AT)
     */
    startedAt: Date | null;
    /**
     * Date when the Actor will time out (ACTOR_TIMEOUT_AT)
     */
    timeoutAt: Date | null;
    /**
     * ID of the key-value store where input and output data of this
     * Actor is stored (ACTOR_DEFAULT_KEY_VALUE_STORE_ID)
     */
    defaultKeyValueStoreId: string | null;
    /**
     * ID of the dataset where input and output data of this
     * Actor is stored (ACTOR_DEFAULT_DATASET_ID)
     */
    defaultDatasetId: string | null;
    /**
     * Amount of memory allocated for the Actor,
     * in megabytes (ACTOR_MEMORY_MBYTES)
     */
    memoryMbytes: number | null;
    /**
     * If set to "1", the web browsers inside the Actor should run in headless
     * mode because there is no windowing system available. (APIFY_HEADLESS)
     */
    headless: string | null;
    /**
     * Is set to "1" if the Actor is running on Apify servers.
     * (APIFY_IS_AT_HOME)
     */
    isAtHome: string | null;
    /**
     * The Apify Proxy password of the user who started the Actor. (APIFY_PROXY_PASSWORD)
     */
    proxyPassword: string | null;
    proxyHostname: string | null;
    proxyPort: string | null;
    /**
     * You can visit this page to troubleshoot your proxy connection. (APIFY_PROXY_STATUS_URL)
     */
    proxyStatusUrl: string | null;
    apiBaseUrl: string | null;
    apiPublicBaseUrl: string | null;
    chromeExecutablePath: string | null;
    dedicatedCpus: string | null;
    disableOutdatedWarning: 1 | null;
    fact: string | null;
    inputSecretsPrivateKeyFile: string | null;
    inputSecretsPrivateKeyPassphrase: string | null;
    /**
     * Defines the path to a local directory where KeyValueStore, Dataset, and RequestQueue
     * store their data. Typically, it is set to ./storage. If omitted, you should define the
     * APIFY_TOKEN environment variable instead. See more info on combination of this and
     * APIFY_TOKEN [here](https://docs.apify.com/sdk/js/docs/guides/environment-variables#combinations-of-apify_local_storage_dir-and-apify_token)(CRAWLEE_STORAGE_DIR)
     */
    localStorageDir: string | null;
    /**
     * Specifies the minimum log level, which can be one of the following values (in order of severity): DEBUG, INFO, WARNING and ERROR
     * (APIFY_LOG_LEVEL)
     */
    logLevel: string | null;
    logFormat: string | null;
    /**
     * Origin for the Actor run, i.e. how it was started. See [here](https://docs.apify.com/sdk/python/reference/enum/MetaOrigin)
     * for more details. (APIFY_META_ORIGIN)
     */
    metaOrigin: string | null;
    /**
     * The key of the input record in the Actor’s default key-value store (ACTOR_INPUT_KEY)
     */
    inputKey: string | null;
    sdkLatestVersion: string | null;
    systemInfoIntervalMillis: string | null;
    workflowKey: string | null;
    actorBuildNumber: string | null;
    actorEventsWsUrl: string | null;
    actorMaxPaidDatasetItems: number | null;
    containerPort: number | null;
    containerUrl: string | null;
    defaultRequestQueueId: string | null;
}
export type UserFunc<T = unknown> = () => Awaitable<T>;
export interface CallOptions extends ActorCallOptions {
    /**
     * User API token that is used to run the Actor. By default, it is taken from the `APIFY_TOKEN` environment variable.
     */
    token?: string;
}
export interface CallTaskOptions extends TaskCallOptions {
    /**
     * User API token that is used to run the Actor. By default, it is taken from the `APIFY_TOKEN` environment variable.
     */
    token?: string;
}
export interface AbortOptions extends RunAbortOptions {
    /**
     * User API token that is used to run the Actor. By default, it is taken from the `APIFY_TOKEN` environment variable.
     */
    token?: string;
    /** Exit with given status message */
    statusMessage?: string;
}
export interface WebhookOptions {
    /**
     * Array of event types, which you can set for Actor run, see
     * the [Actor run events](https://docs.apify.com/webhooks/events#actor-run) in the Apify doc.
     */
    eventTypes: readonly WebhookEventType[];
    /**
     * URL which will be requested using HTTP POST request, when Actor run will reach the set event type.
     */
    requestUrl: string;
    /**
     * Payload template is a JSON-like string that describes the structure of the webhook POST request payload.
     * It uses JSON syntax, extended with a double curly braces syntax for injecting variables `{{variable}}`.
     * Those variables are resolved at the time of the webhook's dispatch, and a list of available variables with their descriptions
     * is available in the [Apify webhook documentation](https://docs.apify.com/webhooks).
     * If `payloadTemplate` is omitted, the default payload template is used
     * ([view docs](https://docs.apify.com/webhooks/actions#payload-template)).
     */
    payloadTemplate?: string;
    /**
     * Idempotency key enables you to ensure that a webhook will not be added multiple times in case of
     * an Actor restart or other situation that would cause the `addWebhook()` function to be called again.
     * We suggest using the Actor run ID as the idempotency key. You can get the run ID by calling
     * {@link Actor.getEnv} function.
     */
    idempotencyKey?: string;
}
export interface MetamorphOptions {
    /**
     * Content type for the `input`. If not specified,
     * `input` is expected to be an object that will be stringified to JSON and content type set to
     * `application/json; charset=utf-8`. If `options.contentType` is specified, then `input` must be a
     * `String` or `Buffer`.
     */
    contentType?: string;
    /**
     * Tag or number of the target Actor build to metamorph into (e.g. `beta` or `1.2.345`).
     * If not provided, the run uses build tag or number from the default Actor run configuration (typically `latest`).
     */
    build?: string;
    /** @internal */
    customAfterSleepMillis?: number;
}
export interface RebootOptions {
    /** @internal */
    customAfterSleepMillis?: number;
}
export interface OpenStorageOptions {
    /**
     * If set to `true` then the cloud storage is used even if the `CRAWLEE_STORAGE_DIR`
     * environment variable is set. This way it is possible to combine local and cloud storage.
     * @default false
     */
    forceCloud?: boolean;
}
export { ClientActorRun as ActorRun };
/**
 * Exit codes for the Actor process.
 * The error codes must be in the range 1-128, to avoid collision with signal exits
 * and to ensure Docker will handle them correctly!
 * @internal should be removed if we decide to remove `Actor.main()`
 */
export declare const EXIT_CODES: {
    SUCCESS: number;
    ERROR_USER_FUNCTION_THREW: number;
    ERROR_UNKNOWN: number;
};
/**
 * `Actor` class serves as an alternative approach to the static helpers exported from the package. It allows to pass configuration
 * that will be used on the instance methods. Environment variables will have precedence over this configuration.
 * See {@link Configuration} for details about what can be configured and what are the default values.
 */
export declare class Actor<Data extends Dictionary = Dictionary> {
    /** @internal */
    static _instance: Actor;
    /**
     * Configuration of this SDK instance (provided to its constructor). See {@link Configuration} for details.
     * @internal
     */
    readonly config: Configuration;
    /**
     * Default {@link ApifyClient} instance.
     * @internal
     */
    readonly apifyClient: ApifyClient;
    /**
     * Default {@link EventManager} instance.
     * @internal
     */
    readonly eventManager: EventManager;
    /**
     * Whether the Actor instance was initialized. This is set by calling {@link Actor.init}.
     */
    initialized: boolean;
    /**
     * Set if the Actor called a method that requires the instance to be initialized, but did not do so.
     * A call to `init` after this warning is emitted is considered  an invalid state and will throw an error.
     */
    private warnedAboutMissingInitCall;
    /**
     * Set if the Actor is currently rebooting.
     */
    private isRebooting;
    private chargingManager;
    constructor(options?: ConfigurationOptions);
    /**
     * Runs the main user function that performs the job of the Actor
     * and terminates the process when the user function finishes.
     *
     * **The `Actor.main()` function is optional** and is provided merely for your convenience.
     * It is mainly useful when you're running your code as an Actor on the [Apify platform](https://apify.com/actors).
     * However, if you want to use Apify SDK tools directly inside your existing projects, e.g.
     * running in an [Express](https://expressjs.com/) server, on
     * [Google Cloud functions](https://cloud.google.com/functions)
     * or [AWS Lambda](https://aws.amazon.com/lambda/), it's better to avoid
     * it since the function terminates the main process when it finishes!
     *
     * The `Actor.main()` function performs the following actions:
     *
     * - When running on the Apify platform (i.e. `APIFY_IS_AT_HOME` environment variable is set),
     *   it sets up a connection to listen for platform events.
     *   For example, to get a notification about an imminent migration to another server.
     *   See {@link Actor.events} for details.
     * - It invokes the user function passed as the `userFunc` parameter.
     * - If the user function returned a promise, waits for it to resolve.
     * - If the user function throws an exception or some other error is encountered,
     *   prints error details to console so that they are stored to the log.
     * - Exits the Node.js process, with zero exit code on success and non-zero on errors.
     *
     * The user function can be synchronous:
     *
     * ```js
     * await Actor.main(() => {
     *   // My synchronous function that returns immediately
     *   console.log('Hello world from Actor!');
     * });
     * ```
     *
     * If the user function returns a promise, it is considered asynchronous:
     * ```js
     * import { gotScraping } from 'got-scraping';
     *
     * await Actor.main(() => {
     *   // My asynchronous function that returns a promise
     *   return gotScraping('http://www.example.com').then((html) => {
     *     console.log(html);
     *   });
     * });
     * ```
     *
     * To simplify your code, you can take advantage of the `async`/`await` keywords:
     *
     * ```js
     * import { gotScraping } from 'got-scraping';
     *
     * await Actor.main(async () => {
     *   // My asynchronous function
     *   const html = await request('http://www.example.com');
     *   console.log(html);
     * });
     * ```
     *
     * @param userFunc User function to be executed. If it returns a promise,
     * the promise will be awaited. The user function is called with no arguments.
     * @param options
     * @ignore
     */
    main<T>(userFunc: UserFunc, options?: MainOptions): Promise<T>;
    /**
     * @ignore
     */
    init(options?: InitOptions): Promise<void>;
    /**
     * @ignore
     */
    exit(messageOrOptions?: string | ExitOptions, options?: ExitOptions): Promise<void>;
    /**
     * @ignore
     */
    fail(messageOrOptions?: string | ExitOptions, options?: ExitOptions): Promise<void>;
    /**
     * @ignore
     */
    on(event: EventTypeName, listener: (...args: any[]) => any): void;
    /**
     * @ignore
     */
    off(event: EventTypeName, listener?: (...args: any[]) => any): void;
    /**
     * Runs an Actor on the Apify platform using the current user account (determined by the `APIFY_TOKEN` environment variable).
     *
     * The result of the function is an {@link ActorRun} object that contains details about the Actor run.
     *
     * If you want to run an Actor task rather than an Actor, please use the {@link Actor.callTask} function instead.
     *
     * For more information about Actors, read the [documentation](https://docs.apify.com/actor).
     *
     * **Example usage:**
     *
     * ```js
     * const run = await Actor.call('apify/hello-world', { myInput: 123 });
     * ```
     *
     * @param actorId
     *  Allowed formats are `username/actor-name`, `userId/actor-name` or Actor ID.
     * @param [input]
     *  Input for the Actor. If it is an object, it will be stringified to
     *  JSON and its content type set to `application/json; charset=utf-8`.
     *  Otherwise the `options.contentType` parameter must be provided.
     * @param [options]
     * @ignore
     */
    call(actorId: string, input?: unknown, options?: CallOptions): Promise<ClientActorRun>;
    /**
     * Runs an Actor on the Apify platform using the current user account (determined by the `APIFY_TOKEN` environment variable),
     * unlike `Actor.call`, this method just starts the run without waiting for finish.
     *
     * The result of the function is an {@link ActorRun} object that contains details about the Actor run.
     *
     * For more information about Actors, read the
     * [documentation](https://docs.apify.com/actor).
     *
     * **Example usage:**
     *
     * ```js
     * const run = await Actor.start('apify/hello-world', { myInput: 123 });
     * ```
     *
     * @param actorId
     *  Allowed formats are `username/actor-name`, `userId/actor-name` or Actor ID.
     * @param [input]
     *  Input for the Actor. If it is an object, it will be stringified to
     *  JSON and its content type set to `application/json; charset=utf-8`.
     *  Otherwise the `options.contentType` parameter must be provided.
     * @param [options]
     * @ignore
     */
    start(actorId: string, input?: unknown, options?: CallOptions): Promise<ClientActorRun>;
    /**
     * Aborts given Actor run on the Apify platform using the current user account (determined by the `APIFY_TOKEN` environment variable).
     *
     * The result of the function is an {@link ActorRun} object that contains details about the Actor run.
     *
     * For more information about Actors, read the
     * [documentation](https://docs.apify.com/actor).
     *
     * **Example usage:**
     *
     * ```js
     * const run = await Actor.abort(runId);
     * ```
     * @ignore
     */
    abort(runId: string, options?: AbortOptions): Promise<ClientActorRun>;
    /**
     * Runs an actor task on the Apify platform using the current user account (determined by the `APIFY_TOKEN` environment variable).
     *
     * The result of the function is an {@link ActorRun} object that contains details about the Actor run.
     *
     * Note that an Actor task is a saved input configuration and options for an Actor.
     * If you want to run an Actor directly rather than an Actor task, please use the
     * {@link Actor.call} function instead.
     *
     * For more information about Actor tasks, read the [documentation](https://docs.apify.com/tasks).
     *
     * **Example usage:**
     *
     * ```js
     * const run = await Actor.callTask('bob/some-task');
     * ```
     *
     * @param taskId
     *  Allowed formats are `username/task-name`, `userId/task-name` or task ID.
     * @param [input]
     *  Input overrides for the Actor task. If it is an object, it will be stringified to
     *  JSON and its content type set to `application/json; charset=utf-8`.
     *  Provided input will be merged with Actor task input.
     * @param [options]
     * @ignore
     */
    callTask(taskId: string, input?: Dictionary, options?: CallTaskOptions): Promise<ClientActorRun>;
    /**
     * Transforms this Actor run to an Actor run of a given Actor. The system stops the current container and starts
     * the new container instead. All the default storages are preserved and the new input is stored under the `INPUT-METAMORPH-1` key
     * in the same default key-value store.
     *
     * @param targetActorId
     *  Either `username/actor-name` or Actor ID of an Actor to which we want to metamorph.
     * @param [input]
     *  Input for the Actor. If it is an object, it will be stringified to
     *  JSON and its content type set to `application/json; charset=utf-8`.
     *  Otherwise, the `options.contentType` parameter must be provided.
     * @param [options]
     * @ignore
     */
    metamorph(targetActorId: string, input?: unknown, options?: MetamorphOptions): Promise<void>;
    /**
     * Internally reboots this Actor. The system stops the current container and starts
     * a new container with the same run ID.
     * This can be used to get the Actor out of irrecoverable error state and continue where it left off.
     *
     * @ignore
     */
    reboot(options?: RebootOptions): Promise<void>;
    /**
     * Creates an ad-hoc webhook for the current Actor run, which lets you receive a notification when the Actor run finished or failed.
     * For more information about Apify Actor webhooks, please see the [documentation](https://docs.apify.com/webhooks).
     *
     * Note that webhooks are only supported for Actors running on the Apify platform.
     * In local environment, the function will print a warning and have no effect.
     *
     * @param options
     * @returns The return value is the Webhook object.
     * For more information, see the [Get webhook](https://apify.com/docs/api/v2#/reference/webhooks/webhook-object/get-webhook) API endpoint.
     * @ignore
     */
    addWebhook(options: WebhookOptions): Promise<Webhook | undefined>;
    /**
     * Sets the status message for the current Actor run.
     *
     * @returns The return value is the Run object.
     * For more information, see the [Actor Runs](https://docs.apify.com/api/v2#/reference/actor-runs/) API endpoints.
     * @ignore
     */
    setStatusMessage(statusMessage: string, options?: SetStatusMessageOptions): Promise<ClientActorRun>;
    /**
     * Stores an object or an array of objects to the default {@link Dataset} of the current Actor run.
     *
     * This is just a convenient shortcut for {@link Dataset.pushData}.
     * For example, calling the following code:
     * ```js
     * await Actor.pushData({ myValue: 123 });
     * ```
     *
     * is equivalent to:
     * ```js
     * const dataset = await Actor.openDataset();
     * await dataset.pushData({ myValue: 123 });
     * ```
     *
     * For more information, see {@link Actor.openDataset} and {@link Dataset.pushData}
     *
     * **IMPORTANT**: Make sure to use the `await` keyword when calling `pushData()`,
     * otherwise the Actor process might finish before the data are stored!
     *
     * @param item Object or array of objects containing data to be stored in the default dataset.
     * The objects must be serializable to JSON and the JSON representation of each object must be smaller than 9MB.
     * @ignore
     */
    pushData(item: Data | Data[]): Promise<void>;
    /**
     * Stores an object or an array of objects to the default {@link Dataset} of the current Actor run.
     *
     * This is just a convenient shortcut for {@link Dataset.pushData}.
     * For example, calling the following code:
     * ```js
     * await Actor.pushData({ myValue: 123 });
     * ```
     *
     * is equivalent to:
     * ```js
     * const dataset = await Actor.openDataset();
     * await dataset.pushData({ myValue: 123 });
     * ```
     *
     * For more information, see {@link Actor.openDataset} and {@link Dataset.pushData}
     *
     * **IMPORTANT**: Make sure to use the `await` keyword when calling `pushData()`,
     * otherwise the Actor process might finish before the data are stored!
     *
     * @param item Object or array of objects containing data to be stored in the default dataset.
     * The objects must be serializable to JSON and the JSON representation of each object must be smaller than 9MB.
     * @param eventName If provided, the method will attempt to charge for the event for each pushed item.
     * @ignore
     */
    pushData(item: Data | Data[], eventName: string): Promise<ChargeResult>;
    /**
     * Opens a dataset and returns a promise resolving to an instance of the {@link Dataset} class.
     *
     * Datasets are used to store structured data where each object stored has the same attributes,
     * such as online store products or real estate offers.
     * The actual data is stored either on the local filesystem or in the cloud.
     *
     * For more details and code examples, see the {@link Dataset} class.
     *
     * @param [datasetIdOrName]
     *   ID or name of the dataset to be opened. If `null` or `undefined`,
     *   the function returns the default dataset associated with the Actor run.
     * @param [options]
     * @ignore
     */
    openDataset(datasetIdOrName?: string | null, options?: OpenStorageOptions): Promise<Dataset<Data>>;
    /**
     * Gets a value from the default {@link KeyValueStore} associated with the current Actor run.
     *
     * This is just a convenient shortcut for {@link KeyValueStore.getValue}.
     * For example, calling the following code:
     * ```js
     * const value = await Actor.getValue('my-key');
     * ```
     *
     * is equivalent to:
     * ```js
     * const store = await Actor.openKeyValueStore();
     * const value = await store.getValue('my-key');
     * ```
     *
     * To store the value to the default key-value store, you can use the {@link Actor.setValue} function.
     *
     * For more information, see  {@link Actor.openKeyValueStore}
     * and  {@link KeyValueStore.getValue}.
     *
     * @param key Unique record key.
     * @returns
     *   Returns a promise that resolves to an object, string
     *   or [`Buffer`](https://nodejs.org/api/buffer.html), depending
     *   on the MIME content type of the record, or `null`
     *   if the record is missing.
     * @ignore
     */
    getValue<T = unknown>(key: string): Promise<T | null>;
    /**
     * Stores or deletes a value in the default {@link KeyValueStore} associated with the current Actor run.
     *
     * This is just a convenient shortcut for  {@link KeyValueStore.setValue}.
     * For example, calling the following code:
     * ```js
     * await Actor.setValue('OUTPUT', { foo: "bar" });
     * ```
     *
     * is equivalent to:
     * ```js
     * const store = await Actor.openKeyValueStore();
     * await store.setValue('OUTPUT', { foo: "bar" });
     * ```
     *
     * To get a value from the default key-value store, you can use the  {@link Actor.getValue} function.
     *
     * For more information, see  {@link Actor.openKeyValueStore}
     * and  {@link KeyValueStore.getValue}.
     *
     * @param key
     *   Unique record key.
     * @param value
     *   Record data, which can be one of the following values:
     *    - If `null`, the record in the key-value store is deleted.
     *    - If no `options.contentType` is specified, `value` can be any JavaScript object, and it will be stringified to JSON.
     *    - If `options.contentType` is set, `value` is taken as is, and it must be a `String` or [`Buffer`](https://nodejs.org/api/buffer.html).
     *   For any other value an error will be thrown.
     * @param [options]
     * @ignore
     */
    setValue<T>(key: string, value: T | null, options?: RecordOptions): Promise<void>;
    /**
     * Gets the Actor input value from the default {@link KeyValueStore} associated with the current Actor run.
     *
     * This is just a convenient shortcut for [`keyValueStore.getValue('INPUT')`](core/class/KeyValueStore#getValue).
     * For example, calling the following code:
     * ```js
     * const input = await Actor.getInput();
     * ```
     *
     * is equivalent to:
     * ```js
     * const store = await Actor.openKeyValueStore();
     * await store.getValue('INPUT');
     * ```
     *
     * Note that the `getInput()` function does not cache the value read from the key-value store.
     * If you need to use the input multiple times in your Actor,
     * it is far more efficient to read it once and store it locally.
     *
     * For more information, see  {@link Actor.openKeyValueStore}
     * and {@link KeyValueStore.getValue}.
     *
     * @returns
     *   Returns a promise that resolves to an object, string
     *   or [`Buffer`](https://nodejs.org/api/buffer.html), depending
     *   on the MIME content type of the record, or `null`
     *   if the record is missing.
     * @ignore
     */
    getInput<T = Dictionary | string | Buffer>(): Promise<T | null>;
    /**
     * Gets the Actor input value just like the {@link Actor.getInput} method,
     * but throws if it is not found.
     */
    getInputOrThrow<T = Dictionary | string | Buffer>(): Promise<T>;
    /**
     * Opens a key-value store and returns a promise resolving to an instance of the {@link KeyValueStore} class.
     *
     * Key-value stores are used to store records or files, along with their MIME content type.
     * The records are stored and retrieved using a unique key.
     * The actual data is stored either on a local filesystem or in the Apify cloud.
     *
     * For more details and code examples, see the {@link KeyValueStore} class.
     *
     * @param [storeIdOrName]
     *   ID or name of the key-value store to be opened. If `null` or `undefined`,
     *   the function returns the default key-value store associated with the Actor run.
     * @param [options]
     * @ignore
     */
    openKeyValueStore(storeIdOrName?: string | null, options?: OpenStorageOptions): Promise<KeyValueStore>;
    /**
     * Opens a request queue and returns a promise resolving to an instance
     * of the {@link RequestQueue} class.
     *
     * {@link RequestQueue} represents a queue of URLs to crawl, which is stored either on local filesystem or in the cloud.
     * The queue is used for deep crawling of websites, where you start with several URLs and then
     * recursively follow links to other pages. The data structure supports both breadth-first
     * and depth-first crawling orders.
     *
     * For more details and code examples, see the {@link RequestQueue} class.
     *
     * @param [queueIdOrName]
     *   ID or name of the request queue to be opened. If `null` or `undefined`,
     *   the function returns the default request queue associated with the Actor run.
     * @param [options]
     * @ignore
     */
    openRequestQueue(queueIdOrName?: string | null, options?: OpenStorageOptions): Promise<RequestQueue>;
    /**
     * Creates a proxy configuration and returns a promise resolving to an instance
     * of the {@link ProxyConfiguration} class that is already initialized.
     *
     * Configures connection to a proxy server with the provided options. Proxy servers are used to prevent target websites from blocking
     * your crawlers based on IP address rate limits or blacklists. Setting proxy configuration in your crawlers automatically configures
     * them to use the selected proxies for all connections.
     *
     * For more details and code examples, see the {@link ProxyConfiguration} class.
     *
     * ```js
     *
     * // Returns initialized proxy configuration class
     * const proxyConfiguration = await Actor.createProxyConfiguration({
     *     groups: ['GROUP1', 'GROUP2'] // List of Apify proxy groups
     *     countryCode: 'US'
     * });
     *
     * const crawler = new CheerioCrawler({
     *   // ...
     *   proxyConfiguration,
     *   requestHandler({ proxyInfo }) {
     *       const usedProxyUrl = proxyInfo.url; // Getting the proxy URL
     *   }
     * })
     *
     * ```
     *
     * For compatibility with existing Actor Input UI (Input Schema), this function
     * returns `undefined` when the following object is passed as `proxyConfigurationOptions`.
     *
     * ```
     * { useApifyProxy: false }
     * ```
     * @ignore
     */
    createProxyConfiguration(proxyConfigurationOptions?: ProxyConfigurationOptions & {
        useApifyProxy?: boolean;
    }): Promise<ProxyConfiguration | undefined>;
    /**
     * Charge for a specified number of events - sub-operations of the Actor.
     *
     * @param options The name of the event to charge for and the number of events to be charged.
     * @ignore
     */
    charge(options: ChargeOptions): Promise<ChargeResult>;
    /**
     * Retrieve the charging manager to access granular pricing information.
     * @ignore
     */
    getChargingManager(): ChargingManager;
    /**
     * Modifies Actor env vars so parsing respects the structure of {@link ApifyEnv} interface.
     */
    private getModifiedActorEnvVars;
    /**
     * Returns a new {@link ApifyEnv} object which contains information parsed from all the Apify environment variables.
     *
     * For the list of the Apify environment variables, see
     * [Actor documentation](https://docs.apify.com/actor/run#environment-variables).
     * If some variables are not defined or are invalid, the corresponding value in the resulting object will be null.
     * @ignore
     */
    getEnv(): ApifyEnv;
    /**
     * Returns a new instance of the Apify API client. The `ApifyClient` class is provided
     * by the [apify-client](https://www.npmjs.com/package/apify-client)
     * NPM package, and it is automatically configured using the `APIFY_API_BASE_URL`, and `APIFY_TOKEN`
     * environment variables. You can override the token via the available options. That's useful
     * if you want to use the client as a different Apify user than the SDK internals are using.
     * @ignore
     */
    newClient(options?: ApifyClientOptions): ApifyClient;
    /**
     * Returns `true` when code is running on Apify platform and `false` otherwise (for example locally).
     * @ignore
     */
    isAtHome(): boolean;
    /**
     * Easily create and manage state values. All state values are automatically persisted.
     *
     * Values can be modified by simply using the assignment operator.
     *
     * @param name The name of the store to use.
     * @param defaultValue If the store does not yet have a value in it, the value will be initialized with the `defaultValue` you provide.
     * @param options An optional object parameter where a custom `keyValueStoreName` and `config` can be passed in.
     */
    useState<State extends Dictionary = Dictionary>(name?: string, defaultValue?: State, options?: UseStateOptions): Promise<State>;
    /**
     * Easily create and manage state values. All state values are automatically persisted.
     *
     * Values can be modified by simply using the assignment operator.
     *
     * @param name The name of the store to use.
     * @param defaultValue If the store does not yet have a value in it, the value will be initialized with the `defaultValue` you provide.
     * @param options An optional object parameter where a custom `keyValueStoreName` and `config` can be passed in.
     */
    static useState<State extends Dictionary = Dictionary>(name?: string, defaultValue?: State, options?: UseStateOptions): Promise<State>;
    /**
     * Runs the main user function that performs the job of the Actor
     * and terminates the process when the user function finishes.
     *
     * **The `Actor.main()` function is optional** and is provided merely for your convenience.
     * It is mainly useful when you're running your code as an Actor on the [Apify platform](https://apify.com/actors).
     * However, if you want to use Apify SDK tools directly inside your existing projects, e.g.
     * running in an [Express](https://expressjs.com/) server, on
     * [Google Cloud functions](https://cloud.google.com/functions)
     * or [AWS Lambda](https://aws.amazon.com/lambda/), it's better to avoid
     * it since the function terminates the main process when it finishes!
     *
     * The `Actor.main()` function performs the following actions:
     *
     * - When running on the Apify platform (i.e. `APIFY_IS_AT_HOME` environment variable is set),
     *   it sets up a connection to listen for platform events.
     *   For example, to get a notification about an imminent migration to another server.
     *   See {@link Actor.events} for details.
     * - It invokes the user function passed as the `userFunc` parameter.
     * - If the user function returned a promise, waits for it to resolve.
     * - If the user function throws an exception or some other error is encountered,
     *   prints error details to console so that they are stored to the log.
     * - Exits the Node.js process, with zero exit code on success and non-zero on errors.
     *
     * The user function can be synchronous:
     *
     * ```js
     * await Actor.main(() => {
     *   // My synchronous function that returns immediately
     *   console.log('Hello world from Actor!');
     * });
     * ```
     *
     * If the user function returns a promise, it is considered asynchronous:
     * ```js
     * import { gotScraping } from 'got-scraping';
     *
     * await Actor.main(() => {
     *   // My asynchronous function that returns a promise
     *   return gotScraping('http://www.example.com').then((html) => {
     *     console.log(html);
     *   });
     * });
     * ```
     *
     * To simplify your code, you can take advantage of the `async`/`await` keywords:
     *
     * ```js
     * import { gotScraping } from 'got-scraping';
     *
     * await Actor.main(async () => {
     *   // My asynchronous function
     *   const html = await gotScraping('http://www.example.com');
     *   console.log(html);
     * });
     * ```
     *
     * @param userFunc User function to be executed. If it returns a promise,
     * the promise will be awaited. The user function is called with no arguments.
     * @param options
     */
    static main<T>(userFunc: UserFunc<T>, options?: MainOptions): Promise<T>;
    /**
     * Initializes the Actor, enabling support for the [Apify platform](https://apify.com/actors) dynamically
     * based on `APIFY_IS_AT_HOME` env var. If you are not running the code on Apify, you don't need to use it.
     * The method will switch storage client implementation automatically, so when you run on the Apify platform,
     * it will use its API instead of the default memory storage. It also increases the available memory ratio
     * from 25% to 100% on the platform.
     *
     * Calling `Actor.exit()` is required if you use the `Actor.init()` method, since it opens websocket connection
     * (see {@link Actor.events} for details), which needs to be terminated for the code to finish.
     *
     * ```js
     * import { gotScraping } from 'got-scraping';
     *
     * await Actor.init();
     *
     * const html = await gotScraping('http://www.example.com');
     * console.log(html);
     *
     * await Actor.exit();
     * ```
     *
     * @param options
     */
    static init(options?: InitOptions): Promise<void>;
    /**
     * Gracefully exits the Actor run with the provided status message and exit code.
     * @param messageOrOptions First parameter accepts either a string (a terminal status message) or an `ExitOptions` object.
     * @param options Second parameter accepts an `ExitOptions` object.
     */
    static exit(messageOrOptions?: string | ExitOptions, options?: ExitOptions): Promise<void>;
    /**
     * Calls `Actor.exit()` with `options.exitCode` set to `1`.
     * @param messageOrOptions First parameter accepts either a string (a terminal status message) or an `ExitOptions` object.
     * @param options Second parameter accepts an `ExitOptions` object.
     */
    static fail(messageOrOptions?: string | ExitOptions, options?: ExitOptions): Promise<void>;
    static on(event: EventTypeName, listener: (...args: any[]) => any): void;
    static off(event: EventTypeName, listener?: (...args: any[]) => any): void;
    /**
     * Runs an Actor on the Apify platform using the current user account (determined by the `APIFY_TOKEN` environment variable).
     *
     * The result of the function is an {@link ActorRun} object that contains details about the Actor run.
     *
     * If you want to run an Actor task rather than an Actor, please use the {@link Actor.callTask} function instead.
     *
     * For more information about Actors, read the [documentation](https://docs.apify.com/actor).
     *
     * **Example usage:**
     *
     * ```js
     * const run = await Actor.call('apify/hello-world', { myInput: 123 });
     * ```
     *
     * @param actorId
     *  Allowed formats are `username/actor-name`, `userId/actor-name` or Actor ID.
     * @param [input]
     *  Input for the Actor. If it is an object, it will be stringified to
     *  JSON and its content type set to `application/json; charset=utf-8`.
     *  Otherwise the `options.contentType` parameter must be provided.
     * @param [options]
     */
    static call(actorId: string, input?: unknown, options?: CallOptions): Promise<ClientActorRun>;
    /**
     * Runs an Actor task on the Apify platform using the current user account (determined by the `APIFY_TOKEN` environment variable).
     *
     * The result of the function is an {@link ActorRun} object that contains details about the Actor run.
     *
     * Note that an Actor task is a saved input configuration and options for an Actor.
     * If you want to run an Actor directly rather than an Actor task, please use the
     * {@link Actor.call} function instead.
     *
     * For more information about Actor tasks, read the [documentation](https://docs.apify.com/tasks).
     *
     * **Example usage:**
     *
     * ```js
     * const run = await Actor.callTask('bob/some-task');
     * ```
     *
     * @param taskId
     *  Allowed formats are `username/task-name`, `userId/task-name` or task ID.
     * @param [input]
     *  Input overrides for the Actor task. If it is an object, it will be stringified to
     *  JSON and its content type set to `application/json; charset=utf-8`.
     *  Provided input will be merged with Actor task input.
     * @param [options]
     */
    static callTask(taskId: string, input?: Dictionary, options?: CallTaskOptions): Promise<ClientActorRun>;
    /**
     * Runs an Actor on the Apify platform using the current user account (determined by the `APIFY_TOKEN` environment variable),
     * unlike `Actor.call`, this method just starts the run without waiting for finish.
     *
     * The result of the function is an {@link ActorRun} object that contains details about the Actor run.
     *
     * For more information about Actors, read the
     * [documentation](https://docs.apify.com/actor).
     *
     * **Example usage:**
     *
     * ```js
     * const run = await Actor.start('apify/hello-world', { myInput: 123 });
     * ```
     *
     * @param actorId
     *  Allowed formats are `username/actor-name`, `userId/actor-name` or Actor ID.
     * @param [input]
     *  Input for the Actor. If it is an object, it will be stringified to
     *  JSON and its content type set to `application/json; charset=utf-8`.
     *  Otherwise the `options.contentType` parameter must be provided.
     * @param [options]
     */
    static start(actorId: string, input?: Dictionary, options?: CallOptions): Promise<ClientActorRun>;
    /**
     * Aborts given Actor run on the Apify platform using the current user account (determined by the `APIFY_TOKEN` environment variable).
     *
     * The result of the function is an {@link ActorRun} object that contains details about the Actor run.
     *
     * For more information about Actors, read the
     * [documentation](https://docs.apify.com/actor).
     *
     * **Example usage:**
     *
     * ```js
     * const run = await Actor.abort(runId);
     * ```
     */
    static abort(runId: string, options?: AbortOptions): Promise<ClientActorRun>;
    /**
     * Transforms this Actor run to an Actor run of a given Actor. The system stops the current container and starts
     * the new container instead. All the default storages are preserved and the new input is stored under the `INPUT-METAMORPH-1` key
     * in the same default key-value store.
     *
     * @param targetActorId
     *  Either `username/actor-name` or Actor ID of an Actor to which we want to metamorph.
     * @param [input]
     *  Input for the Actor. If it is an object, it will be stringified to
     *  JSON and its content type set to `application/json; charset=utf-8`.
     *  Otherwise, the `options.contentType` parameter must be provided.
     * @param [options]
     */
    static metamorph(targetActorId: string, input?: unknown, options?: MetamorphOptions): Promise<void>;
    /**
     * Internally reboots this Actor run. The system stops the current container and starts
     * a new container with the same run id.
     * This can be used to get the Actor out of irrecoverable error state and continue where it left off.
     */
    static reboot(options?: RebootOptions): Promise<void>;
    /**
     * Creates an ad-hoc webhook for the current Actor run, which lets you receive a notification when the Actor run finished or failed.
     * For more information about Apify Actor webhooks, please see the [documentation](https://docs.apify.com/webhooks).
     *
     * Note that webhooks are only supported for Actors running on the Apify platform.
     * In local environment, the function will print a warning and have no effect.
     *
     * @param options
     * @returns The return value is the Webhook object.
     * For more information, see the [Get webhook](https://apify.com/docs/api/v2#/reference/webhooks/webhook-object/get-webhook) API endpoint.
     */
    static addWebhook(options: WebhookOptions): Promise<Webhook | undefined>;
    /**
     * Sets the status message for the current Actor run.
     *
     * @param statusMessage The status message to set.
     * @param [options]
     * @param [options.isStatusMessageTerminal] If `true`, the status message will be marked as terminal.
     *          This is required for the last status message of the run. Default value is `false`.
     * @returns The return value is the Run object. When run locally, this method returns empty object (`{}`).
     * For more information, see the [Actor Runs](https://docs.apify.com/api/v2#/reference/actor-runs/) API endpoints.
     */
    static setStatusMessage(statusMessage: string, options?: SetStatusMessageOptions): Promise<ClientActorRun>;
    /**
     * Stores an object or an array of objects to the default {@link Dataset} of the current Actor run.
     *
     * This is just a convenient shortcut for {@link Dataset.pushData}.
     * For example, calling the following code:
     * ```js
     * await Actor.pushData({ myValue: 123 });
     * ```
     *
     * is equivalent to:
     * ```js
     * const dataset = await Actor.openDataset();
     * await dataset.pushData({ myValue: 123 });
     * ```
     *
     * For more information, see {@link Actor.openDataset} and {@link Dataset.pushData}
     *
     * **IMPORTANT**: Make sure to use the `await` keyword when calling `pushData()`,
     * otherwise the Actor process might finish before the data are stored!
     *
     * @param item Object or array of objects containing data to be stored in the default dataset.
     * The objects must be serializable to JSON and the JSON representation of each object must be smaller than 9MB.
     */
    static pushData<Data extends Dictionary = Dictionary>(item: Data | Data[]): Promise<void>;
    /**
     * Stores an object or an array of objects to the default {@link Dataset} of the current Actor run.
     *
     * This is just a convenient shortcut for {@link Dataset.pushData}.
     * For example, calling the following code:
     * ```js
     * await Actor.pushData({ myValue: 123 });
     * ```
     *
     * is equivalent to:
     * ```js
     * const dataset = await Actor.openDataset();
     * await dataset.pushData({ myValue: 123 });
     * ```
     *
     * For more information, see {@link Actor.openDataset} and {@link Dataset.pushData}
     *
     * **IMPORTANT**: Make sure to use the `await` keyword when calling `pushData()`,
     * otherwise the Actor process might finish before the data are stored!
     *
     * @param item Object or array of objects containing data to be stored in the default dataset.
     * The objects must be serializable to JSON and the JSON representation of each object must be smaller than 9MB.
     * @param eventName If provided, the method will attempt to charge for the event for each pushed item.
     */
    static pushData<Data extends Dictionary = Dictionary>(item: Data | Data[], eventName: string): Promise<ChargeResult>;
    /**
     * Opens a dataset and returns a promise resolving to an instance of the {@link Dataset} class.
     *
     * Datasets are used to store structured data where each object stored has the same attributes,
     * such as online store products or real estate offers.
     * The actual data is stored either on the local filesystem or in the cloud.
     *
     * For more details and code examples, see the {@link Dataset} class.
     *
     * @param [datasetIdOrName]
     *   ID or name of the dataset to be opened. If `null` or `undefined`,
     *   the function returns the default dataset associated with the Actor run.
     * @param [options]
     */
    static openDataset<Data extends Dictionary = Dictionary>(datasetIdOrName?: string | null, options?: OpenStorageOptions): Promise<Dataset<Data>>;
    /**
     * Gets a value from the default {@link KeyValueStore} associated with the current Actor run.
     *
     * This is just a convenient shortcut for {@link KeyValueStore.getValue}.
     * For example, calling the following code:
     * ```js
     * const value = await Actor.getValue('my-key');
     * ```
     *
     * is equivalent to:
     * ```js
     * const store = await Actor.openKeyValueStore();
     * const value = await store.getValue('my-key');
     * ```
     *
     * To store the value to the default key-value store, you can use the {@link Actor.setValue} function.
     *
     * For more information, see  {@link Actor.openKeyValueStore}
     * and  {@link KeyValueStore.getValue}.
     *
     * @param key Unique record key.
     * @returns
     *   Returns a promise that resolves to an object, string
     *   or [`Buffer`](https://nodejs.org/api/buffer.html), depending
     *   on the MIME content type of the record, or `null`
     *   if the record is missing.
     */
    static getValue<T = unknown>(key: string): Promise<T | null>;
    /**
     * Stores or deletes a value in the default {@link KeyValueStore} associated with the current Actor run.
     *
     * This is just a convenient shortcut for  {@link KeyValueStore.setValue}.
     * For example, calling the following code:
     * ```js
     * await Actor.setValue('OUTPUT', { foo: "bar" });
     * ```
     *
     * is equivalent to:
     * ```js
     * const store = await Actor.openKeyValueStore();
     * await store.setValue('OUTPUT', { foo: "bar" });
     * ```
     *
     * To get a value from the default key-value store, you can use the  {@link Actor.getValue} function.
     *
     * For more information, see  {@link Actor.openKeyValueStore}
     * and  {@link KeyValueStore.getValue}.
     *
     * @param key
     *   Unique record key.
     * @param value
     *   Record data, which can be one of the following values:
     *    - If `null`, the record in the key-value store is deleted.
     *    - If no `options.contentType` is specified, `value` can be any JavaScript object, and it will be stringified to JSON.
     *    - If `options.contentType` is set, `value` is taken as is, and it must be a `String` or [`Buffer`](https://nodejs.org/api/buffer.html).
     *   For any other value an error will be thrown.
     * @param [options]
     */
    static setValue<T>(key: string, value: T | null, options?: RecordOptions): Promise<void>;
    /**
     * Gets the Actor input value from the default {@link KeyValueStore} associated with the current Actor run.
     *
     * This is just a convenient shortcut for {@link KeyValueStore.getValue | `keyValueStore.getValue('INPUT')`}.
     * For example, calling the following code:
     * ```js
     * const input = await Actor.getInput();
     * ```
     *
     * is equivalent to:
     * ```js
     * const store = await Actor.openKeyValueStore();
     * await store.getValue('INPUT');
     * ```
     *
     * Note that the `getInput()` function does not cache the value read from the key-value store.
     * If you need to use the input multiple times in your Actor,
     * it is far more efficient to read it once and store it locally.
     *
     * For more information, see {@link Actor.openKeyValueStore} and {@link KeyValueStore.getValue}.
     *
     * @returns
     *   Returns a promise that resolves to an object, string
     *   or [`Buffer`](https://nodejs.org/api/buffer.html), depending
     *   on the MIME content type of the record, or `null`
     *   if the record is missing.
     */
    static getInput<T = Dictionary | string | Buffer>(): Promise<T | null>;
    /**
     * Gets the Actor input value just like the {@link Actor.getInput} method,
     * but throws if it is not found.
     */
    static getInputOrThrow<T = Dictionary | string | Buffer>(): Promise<T>;
    /**
     * Opens a key-value store and returns a promise resolving to an instance of the {@link KeyValueStore} class.
     *
     * Key-value stores are used to store records or files, along with their MIME content type.
     * The records are stored and retrieved using a unique key.
     * The actual data is stored either on a local filesystem or in the Apify cloud.
     *
     * For more details and code examples, see the {@link KeyValueStore} class.
     *
     * @param [storeIdOrName]
     *   ID or name of the key-value store to be opened. If `null` or `undefined`,
     *   the function returns the default key-value store associated with the Actor run.
     * @param [options]
     */
    static openKeyValueStore(storeIdOrName?: string | null, options?: OpenStorageOptions): Promise<KeyValueStore>;
    /**
     * Opens a request queue and returns a promise resolving to an instance
     * of the {@link RequestQueue} class.
     *
     * {@link RequestQueue} represents a queue of URLs to crawl, which is stored either on local filesystem or in the cloud.
     * The queue is used for deep crawling of websites, where you start with several URLs and then
     * recursively follow links to other pages. The data structure supports both breadth-first
     * and depth-first crawling orders.
     *
     * For more details and code examples, see the {@link RequestQueue} class.
     *
     * @param [queueIdOrName]
     *   ID or name of the request queue to be opened. If `null` or `undefined`,
     *   the function returns the default request queue associated with the Actor run.
     * @param [options]
     */
    static openRequestQueue(queueIdOrName?: string | null, options?: OpenStorageOptions): Promise<RequestQueue>;
    /**
     * Creates a proxy configuration and returns a promise resolving to an instance
     * of the {@link ProxyConfiguration} class that is already initialized.
     *
     * Configures connection to a proxy server with the provided options. Proxy servers are used to prevent target websites from blocking
     * your crawlers based on IP address rate limits or blacklists. Setting proxy configuration in your crawlers automatically configures
     * them to use the selected proxies for all connections.
     *
     * For more details and code examples, see the {@link ProxyConfiguration} class.
     *
     * ```js
     *
     * // Returns initialized proxy configuration class
     * const proxyConfiguration = await Actor.createProxyConfiguration({
     *     groups: ['GROUP1', 'GROUP2'] // List of Apify proxy groups
     *     countryCode: 'US'
     * });
     *
     * const crawler = new CheerioCrawler({
     *   // ...
     *   proxyConfiguration,
     *   requestHandler({ proxyInfo }) {
     *       const usedProxyUrl = proxyInfo.url; // Getting the proxy URL
     *   }
     * })
     *
     * ```
     *
     * For compatibility with existing Actor Input UI (Input Schema), this function
     * returns `undefined` when the following object is passed as `proxyConfigurationOptions`.
     *
     * ```
     * { useApifyProxy: false }
     * ```
     */
    static createProxyConfiguration(proxyConfigurationOptions?: ProxyConfigurationOptions & {
        useApifyProxy?: boolean;
    }): Promise<ProxyConfiguration | undefined>;
    /**
     * Charge for a specified number of events - sub-operations of the Actor.
     *
     * @param options The name of the event to charge for and the number of events to be charged.
     */
    static charge(options: ChargeOptions): Promise<ChargeResult>;
    /**
     * Retrieve the charging manager to access granular pricing information.
     */
    static getChargingManager(): ChargingManager;
    /**
     * Returns a new {@link ApifyEnv} object which contains information parsed from all the Apify environment variables.
     *
     * For the list of the Apify environment variables, see
     * [Actor documentation](https://docs.apify.com/actor/run#environment-variables).
     * If some of the variables are not defined or are invalid, the corresponding value in the resulting object will be null.
     */
    static getEnv(): ApifyEnv;
    /**
     * Returns a new instance of the Apify API client. The `ApifyClient` class is provided
     * by the [apify-client](https://www.npmjs.com/package/apify-client)
     * NPM package, and it is automatically configured using the `APIFY_API_BASE_URL`, and `APIFY_TOKEN`
     * environment variables. You can override the token via the available options. That's useful
     * if you want to use the client as a different Apify user than the SDK internals are using.
     */
    static newClient(options?: ApifyClientOptions): ApifyClient;
    /**
     * Returns `true` when code is running on Apify platform and `false` otherwise (for example locally).
     */
    static isAtHome(): boolean;
    /** Default {@link ApifyClient} instance. */
    static get apifyClient(): ApifyClient;
    /** Default {@link Configuration} instance. */
    static get config(): Configuration;
    /** @internal */
    static getDefaultInstance(): Actor;
    private _openStorage;
    private _ensureActorInit;
}
//# sourceMappingURL=actor.d.ts.map