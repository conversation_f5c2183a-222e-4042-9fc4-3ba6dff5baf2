/**
 * Gets info about system, node version and apify package version.
 * @internal
 */
export declare function getSystemInfo(): {
    apifyVersion: any;
    apifyClientVersion: any;
    crawleeVersion: any;
    osType: string;
    nodeVersion: string;
};
/**
 * @internal
 */
export declare function checkCrawleeVersion(): void;
/**
 * Prints a warning if this version of Apify SDK is outdated.
 * @ignore
 */
export declare function printOutdatedSdkWarning(): void;
//# sourceMappingURL=utils.d.ts.map