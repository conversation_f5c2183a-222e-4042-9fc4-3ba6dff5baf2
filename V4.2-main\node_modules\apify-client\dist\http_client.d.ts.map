{"version": 3, "file": "http_client.d.ts", "sourceRoot": "", "sources": ["../src/http_client.ts"], "names": [], "mappings": "AAEA,OAAO,cAAc,MAAM,gBAAgB,CAAC;AAG5C,OAAO,KAAK,EAAc,aAAa,EAAE,kBAAkB,EAAE,aAAa,EAAE,0BAA0B,EAAE,MAAM,OAAO,CAAC;AAItH,OAAO,KAAK,EAAE,GAAG,EAAE,MAAM,YAAY,CAAC;AAGtC,OAAO,KAAK,EAAE,0BAA0B,EAAE,MAAM,gBAAgB,CAAC;AAEjE,OAAO,KAAK,EAAE,UAAU,EAAE,MAAM,cAAc,CAAC;AAO/C,qBAAa,UAAU;IACnB,KAAK,EAAE,UAAU,CAAC;IAElB,UAAU,EAAE,MAAM,CAAC;IAEnB,4BAA4B,EAAE,MAAM,CAAC;IAErC,+BAA+B,EAAE,0BAA0B,EAAE,CAAC;IAE9D,MAAM,EAAE,GAAG,CAAC;IAEZ,aAAa,EAAE,MAAM,CAAC;IAEtB,SAAS,CAAC,EAAE,cAAc,CAAC;IAE3B,UAAU,CAAC,EAAE,cAAc,CAAC,UAAU,CAAC;IAEvC,KAAK,EAAE,aAAa,CAAC;IAErB,WAAW,CAAC,EAAE,MAAM,CAAC;gBAET,OAAO,EAAE,iBAAiB;IAkFhC,IAAI,CAAC,CAAC,GAAG,GAAG,EAAE,MAAM,EAAE,kBAAkB,GAAG,OAAO,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC;IAW1E,OAAO,CAAC,yBAAyB;IASjC;;;;OAIG;IACH,OAAO,CAAC,qBAAqB;IA+C7B,OAAO,CAAC,WAAW;IAInB;;;OAGG;IACH,OAAO,CAAC,mBAAmB;IAe3B;;;OAGG;IACH,OAAO,CAAC,eAAe;IAIvB;;;;;OAKG;IACH,OAAO,CAAC,iBAAiB;IAIzB;;;;OAIG;IACH,OAAO,CAAC,sBAAsB;IAI9B;;;;OAIG;IACH,OAAO,CAAC,eAAe;IAMvB;;;;OAIG;IACH,OAAO,CAAC,sBAAsB;IAM9B,OAAO,CAAC,eAAe;CAO1B;AAED,MAAM,WAAW,kBAAmB,SAAQ,kBAAkB;IAC1D,kBAAkB,CAAC,EAAE,OAAO,CAAC;IAC7B,WAAW,CAAC,EAAE,OAAO,CAAC;IACtB,kBAAkB,CAAC,EAAE,OAAO,CAAC;CAChC;AAED,MAAM,WAAW,aAAa,CAAC,CAAC,GAAG,GAAG,CAAE,SAAQ,aAAa,CAAC,CAAC,CAAC;IAC5D,MAAM,EAAE,kBAAkB,GAAG,0BAA0B,CAAC;CAC3D;AAED,MAAM,WAAW,iBAAiB;IAC9B,gBAAgB,EAAE,UAAU,CAAC;IAC7B,UAAU,EAAE,MAAM,CAAC;IACnB,4BAA4B,EAAE,MAAM,CAAC;IACrC,mBAAmB,EAAE,0BAA0B,EAAE,CAAC;IAClD,WAAW,EAAE,MAAM,CAAC;IACpB,MAAM,EAAE,GAAG,CAAC;IACZ,KAAK,CAAC,EAAE,MAAM,CAAC;IACf,WAAW,CAAC,EAAE,MAAM,CAAC;IACrB,gBAAgB;IAChB,eAAe,CAAC,EAAE,MAAM,GAAG,MAAM,EAAE,CAAC;CACvC"}