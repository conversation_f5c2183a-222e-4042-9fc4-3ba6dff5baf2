{"version": 3, "file": "platform_event_manager.js", "sourceRoot": "", "sources": ["../src/platform_event_manager.ts"], "names": [], "mappings": ";;;AAAA,wCAAwD;AACxD,2BAA+B;AAE/B,0CAAkE;AAClE,gDAAuD;AAEvD,yDAAmD;AAEnD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAqCG;AACH,MAAa,oBAAqB,SAAQ,mBAAY;IAIlD,YAA8B,SAAS,gCAAa,CAAC,eAAe,EAAE;QAClE,KAAK,EAAE,CAAC;QADA;;;;mBAAkB,MAAM;WAAkC;QAHtE,4CAA4C;QACpC;;;;;WAAqB;IAI7B,CAAC;IAED;;;OAGG;IACM,KAAK,CAAC,IAAI;QACf,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC;YACnB,OAAO;QACX,CAAC;QAED,MAAM,KAAK,CAAC,IAAI,EAAE,CAAC;QACnB,MAAM,WAAW,GAAG,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,kBAAkB,CAAC,CAAC;QAExD,0EAA0E;QAC1E,IAAI,CAAC,WAAW,EAAE,CAAC;YACf,IAAI,CAAC,GAAG,CAAC,KAAK,CACV,wBAAwB,uBAAc,CAAC,oBAAoB,6DAA6D,CAC3H,CAAC;YACF,OAAO;QACX,CAAC;QAED,IAAI,CAAC,yBAAyB,CAAC,WAAW,CAAC,CAAC;IAChD,CAAC;IAEO,yBAAyB,CAAC,WAAmB;QACjD,IAAI,CAAC,QAAQ,GAAG,IAAI,cAAS,CAAC,WAAW,CAAC,CAAC;QAC3C,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,SAAS,EAAE,CAAC,OAAO,EAAE,EAAE;YACpC,IAAI,CAAC,OAAO;gBAAE,OAAO;YAErB,IAAI,CAAC;gBACD,MAAM,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC;gBACnD,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;gBAE7B,IAAI,IAAI,KAAK,0BAAiB,CAAC,SAAS,EAAE,CAAC;oBACvC,IAAA,+BAAmB,EAAC,IAAI,CAAC,SAAS,CAAC,YAAa,CAAC,CAAC,CAAC,4CAA4C;oBAC/F,IAAI,CAAC,MAAM,CAAC,IAAI,+CAA0B;wBACtC,WAAW,EAAE,IAAI;qBACpB,CAAC,CAAC;gBACP,CAAC;YACL,CAAC;YAAC,OAAO,GAAG,EAAE,CAAC;gBACX,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC,GAAY,EAAE,0BAA0B,CAAC,CAAC;YACjE,CAAC;QACL,CAAC,CAAC,CAAC;QACH,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,OAAO,EAAE,CAAC,GAAG,EAAE,EAAE;YAC9B,iFAAiF;YACjF,IACI,GAAG,CAAC,OAAO;gBACX,4DAA4D;gBAE5D,OAAO;YAEX,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC,GAAG,EAAE,8BAA8B,CAAC,CAAC;QAC5D,CAAC,CAAC,CAAC;QACH,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,OAAO,EAAE,GAAG,EAAE;YAC3B,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,4BAA4B,CAAC,CAAC;YAC7C,IAAI,CAAC,QAAQ,GAAG,SAAS,CAAC;QAC9B,CAAC,CAAC,CAAC;IACP,CAAC;IAED;;;;OAIG;IACM,KAAK,CAAC,KAAK;QAChB,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC;YACpB,OAAO;QACX,CAAC;QAED,MAAM,KAAK,CAAC,KAAK,EAAE,CAAC;QACpB,IAAI,CAAC,QAAQ,EAAE,KAAK,EAAE,CAAC;IAC3B,CAAC;CACJ;AA/ED,oDA+EC"}