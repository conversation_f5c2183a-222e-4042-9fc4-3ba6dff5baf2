import type { ConfigurationOptions as CoreConfigurationOptions } from '@crawlee/core';
import { Configuration as CoreConfiguration } from '@crawlee/core';
import type { META_ORIGINS } from '@apify/consts';
export interface ConfigurationOptions extends CoreConfigurationOptions {
    metamorphAfterSleepMillis?: number;
    actorEventsWsUrl?: string;
    token?: string;
    actorId?: string;
    actorRunId?: string;
    actorTaskId?: string;
    apiBaseUrl?: string;
    apiPublicBaseUrl?: string;
    containerPort?: number;
    containerUrl?: string;
    proxyHostname?: string;
    proxyPassword?: string;
    proxyPort?: number;
    proxyStatusUrl?: string;
    /**
     * @deprecated use `containerPort` instead
     */
    standbyPort?: number;
    standbyUrl?: string;
    isAtHome?: boolean;
    userId?: string;
    inputSecretsPrivateKeyPassphrase?: string;
    inputSecretsPrivateKeyFile?: string;
    maxTotalChargeUsd?: number;
    metaOrigin?: (typeof META_ORIGINS)[keyof typeof META_ORIGINS];
    testPayPerEvent?: boolean;
    useChargingLogDataset?: boolean;
}
/**
 * `Configuration` is a value object holding the SDK configuration. We can use it in two ways:
 *
 * 1. When using `Actor` class, we can get the instance configuration via `sdk.config`
 *
 *    ```javascript
 *    import { Actor } from 'apify';
 *    import { BasicCrawler } from 'crawlee';
 *
 *    const sdk = new Actor({ token: '123' });
 *    console.log(sdk.config.get('token')); // '123'
 *
 *    const crawler = new BasicCrawler({
 *        // ... crawler options
 *    }, sdk.config);
 *    ```
 *
 * 2. To get the global configuration (singleton instance). It will respect the environment variables.
 *
 *    ```javascript
 *    import { BasicCrawler, Configuration } from 'crawlee';
 *
 *    // Get the global configuration
 *    const config = Configuration.getGlobalConfig();
 *    // Set the 'persistStateIntervalMillis' option
 *    // of global configuration to 30 seconds
 *    config.set('persistStateIntervalMillis', 30_000);
 *
 *    // No need to pass the configuration to the crawler,
 *    // as it's using the global configuration by default
 *    const crawler = new BasicCrawler();
 *    ```
 *
 * ## Supported Configuration Options
 *
 * Key | Environment Variable | Default Value
 * ---|---|---
 * `memoryMbytes` | `ACTOR_MEMORY_MBYTES` | -
 * `headless` | `APIFY_HEADLESS` | -
 * `persistStateIntervalMillis` | `APIFY_PERSIST_STATE_INTERVAL_MILLIS` | `60e3`
 * `token` | `APIFY_TOKEN` | -
 * `isAtHome` | `APIFY_IS_AT_HOME` | -
 * `defaultDatasetId` | `ACTOR_DEFAULT_DATASET_ID` | `'default'`
 * `defaultKeyValueStoreId` | `ACTOR_DEFAULT_KEY_VALUE_STORE_ID` | `'default'`
 * `defaultRequestQueueId` | `ACTOR_DEFAULT_REQUEST_QUEUE_ID` | `'default'`
 *
 * ## Advanced Configuration Options
 *
 * Key | Environment Variable | Default Value
 * ---|---|---
 * `actorEventsWsUrl` | `ACTOR_EVENTS_WEBSOCKET_URL` | -
 * `actorId` | `ACTOR_ID` | -
 * `actorRunId` | `ACTOR_RUN_ID` | -
 * `actorTaskId` | `ACTOR_TASK_ID` | -
 * `apiBaseUrl` | `APIFY_API_BASE_URL` | `'https://api.apify.com'`
 * `containerPort` | `ACTOR_WEB_SERVER_PORT` | `4321`
 * `containerUrl` | `ACTOR_WEB_SERVER_URL` | `'http://localhost:4321'`
 * `inputKey` | `ACTOR_INPUT_KEY` | `'INPUT'`
 * `metamorphAfterSleepMillis` | `APIFY_METAMORPH_AFTER_SLEEP_MILLIS` | `300e3`
 * `metaOrigin` | `APIFY_META_ORIGIN` | -
 * `proxyHostname` | `APIFY_PROXY_HOSTNAME` | `'proxy.apify.com'`
 * `proxyPassword` | `APIFY_PROXY_PASSWORD` | -
 * `proxyPort` | `APIFY_PROXY_PORT` | `8000`
 * `proxyStatusUrl` | `APIFY_PROXY_STATUS_URL` | `'http://proxy.apify.com'`
 * `userId` | `APIFY_USER_ID` | -
 * `xvfb` | `APIFY_XVFB` | -
 * `standbyPort` | `ACTOR_STANDBY_PORT` | `4321`
 * `standbyUrl` | `ACTOR_STANDBY_URL` | -
 * `chromeExecutablePath` | `APIFY_CHROME_EXECUTABLE_PATH` | -
 * `defaultBrowserPath` | `APIFY_DEFAULT_BROWSER_PATH` | -
 */
export declare class Configuration extends CoreConfiguration {
    /** @inheritDoc */
    static globalConfig?: Configuration;
    protected static ENV_MAP: {
        APIFY_AVAILABLE_MEMORY_RATIO: string;
        APIFY_PURGE_ON_START: string;
        APIFY_MEMORY_MBYTES: string;
        APIFY_DEFAULT_DATASET_ID: string;
        APIFY_DEFAULT_KEY_VALUE_STORE_ID: string;
        APIFY_DEFAULT_REQUEST_QUEUE_ID: string;
        APIFY_INPUT_KEY: string;
        APIFY_PERSIST_STATE_INTERVAL_MILLIS: string;
        APIFY_HEADLESS: string;
        APIFY_XVFB: string;
        APIFY_CHROME_EXECUTABLE_PATH: string;
        APIFY_DEFAULT_BROWSER_PATH: string;
        APIFY_DISABLE_BROWSER_SANDBOX: string;
        APIFY_TOKEN: string;
        APIFY_METAMORPH_AFTER_SLEEP_MILLIS: string;
        APIFY_TEST_PERSIST_INTERVAL_MILLIS: string;
        APIFY_ACTOR_EVENTS_WS_URL: string;
        APIFY_ACTOR_ID: string;
        APIFY_API_BASE_URL: string;
        APIFY_API_PUBLIC_BASE_URL: string;
        APIFY_IS_AT_HOME: string;
        APIFY_ACTOR_RUN_ID: string;
        APIFY_ACTOR_TASK_ID: string;
        APIFY_CONTAINER_PORT: string;
        APIFY_CONTAINER_URL: string;
        APIFY_USER_ID: string;
        APIFY_PROXY_HOSTNAME: string;
        APIFY_PROXY_PASSWORD: string;
        APIFY_PROXY_STATUS_URL: string;
        APIFY_PROXY_PORT: string;
        APIFY_INPUT_SECRETS_PRIVATE_KEY_FILE: string;
        APIFY_INPUT_SECRETS_PRIVATE_KEY_PASSPHRASE: string;
        APIFY_META_ORIGIN: string;
        ACTOR_DEFAULT_DATASET_ID: string;
        ACTOR_DEFAULT_KEY_VALUE_STORE_ID: string;
        ACTOR_DEFAULT_REQUEST_QUEUE_ID: string;
        ACTOR_EVENTS_WEBSOCKET_URL: string;
        ACTOR_ID: string;
        ACTOR_INPUT_KEY: string;
        ACTOR_MEMORY_MBYTES: string;
        ACTOR_RUN_ID: string;
        ACTOR_STANDBY_PORT: string;
        ACTOR_STANDBY_URL: string;
        ACTOR_TASK_ID: string;
        ACTOR_WEB_SERVER_PORT: string;
        ACTOR_WEB_SERVER_URL: string;
        ACTOR_MAX_TOTAL_CHARGE_USD: string;
        ACTOR_TEST_PAY_PER_EVENT: string;
        ACTOR_USE_CHARGING_LOG_DATASET: string;
    };
    protected static INTEGER_VARS: string[];
    protected static BOOLEAN_VARS: string[];
    protected static DEFAULTS: {
        defaultKeyValueStoreId: string;
        defaultDatasetId: string;
        defaultRequestQueueId: string;
        inputKey: string;
        apiBaseUrl: string;
        apiPublicBaseUrl: string;
        proxyStatusUrl: string;
        proxyHostname: string;
        proxyPort: number;
        containerPort: number;
        containerUrl: string;
        standbyPort: number;
        metamorphAfterSleepMillis: number;
        persistStateIntervalMillis: number;
        testPayPerEvent: boolean;
        useChargingLogDataset: boolean;
    };
    /**
     * @inheritDoc
     */
    get<T extends keyof ConfigurationOptions, U extends ConfigurationOptions[T]>(key: T, defaultValue?: U): U;
    /**
     * @inheritDoc
     */
    set(key: keyof ConfigurationOptions, value?: any): void;
    /**
     * @inheritDoc
     */
    static getGlobalConfig(): Configuration;
    /**
     * Resets global configuration instance. The default instance holds configuration based on env vars,
     * if we want to change them, we need to first reset the global state. Used mainly for testing purposes.
     */
    static resetGlobalState(): void;
}
//# sourceMappingURL=configuration.d.ts.map