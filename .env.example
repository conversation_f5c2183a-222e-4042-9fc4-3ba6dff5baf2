# Database Configuration
DATABASE_URL="postgresql://username:password@localhost:5432/outlook_catchall_db"

# Server Configuration
PORT=3000
NODE_ENV=development

# CORS Configuration
ALLOWED_ORIGINS="http://localhost:3000,http://localhost:3001"

# JWT Configuration (if using authentication)
JWT_SECRET="your-super-secret-jwt-key-here"
JWT_EXPIRES_IN="7d"

# Email API Configuration
EMAIL_API_URL="https://your-email-api.com/webhook"
EMAIL_API_TIMEOUT=30000

# Outlook API Configuration
OUTLOOK_API_ENDPOINTS="https://api1.example.com,https://api2.example.com"
OUTLOOK_CLIENT_ID="your-outlook-client-id"
OUTLOOK_CLIENT_SECRET="your-outlook-client-secret"

# Rate Limiting
MAX_EMAILS_PER_MINUTE=1000
BATCH_SIZE=1000
ENABLE_FAILOVER=true
HEALTH_CHECK_INTERVAL=60000

# Pricing Configuration
DEFAULT_PRICE_PER_10K=20.00
DEFAULT_CREDITS_PER_UNIT=10000
CREDIT_EXPIRY_DAYS=30

# Logging
LOG_LEVEL=info

# Cleanup Job Configuration
CLEANUP_CRON_SCHEDULE="0 2 * * *"

# Payment Gateway (if integrating with Stripe, PayPal, etc.)
STRIPE_SECRET_KEY="sk_test_..."
STRIPE_PUBLISHABLE_KEY="pk_test_..."
STRIPE_WEBHOOK_SECRET="whsec_..."

# Monitoring (optional)
SENTRY_DSN="your-sentry-dsn"
NEW_RELIC_LICENSE_KEY="your-new-relic-key"
