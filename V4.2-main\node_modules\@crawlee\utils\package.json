{"name": "@crawlee/utils", "version": "3.13.9", "description": "A set of shared utilities that can be used by crawlers", "engines": {"node": ">=16.0.0"}, "main": "./index.js", "module": "./index.mjs", "types": "./index.d.ts", "exports": {".": {"import": "./index.mjs", "require": "./index.js", "types": "./index.d.ts"}, "./package.json": "./package.json"}, "keywords": ["apify", "crawlers", "crawling", "utilities", "utils"], "author": {"name": "Apify", "email": "<EMAIL>", "url": "https://apify.com"}, "contributors": ["<PERSON><PERSON> <<EMAIL>>"], "license": "Apache-2.0", "repository": {"type": "git", "url": "git+https://github.com/apify/crawlee"}, "bugs": {"url": "https://github.com/apify/crawlee/issues"}, "homepage": "https://crawlee.dev", "scripts": {"build": "yarn clean && yarn compile && yarn copy", "clean": "rimraf ./dist", "compile": "tsc -p tsconfig.build.json && gen-esm-wrapper ./index.js ./index.mjs", "copy": "tsx ../../scripts/copy.ts"}, "dependencies": {"@apify/log": "^2.4.0", "@apify/ps-tree": "^1.2.0", "@crawlee/types": "3.13.9", "@types/sax": "^1.2.7", "cheerio": "1.0.0-rc.12", "file-type": "^20.0.0", "got-scraping": "^4.0.3", "ow": "^0.28.1", "robots-parser": "^3.0.1", "sax": "^1.4.1", "tslib": "^2.4.0", "whatwg-mimetype": "^4.0.0"}, "devDependencies": {"@types/whatwg-mimetype": "^3.0.2"}, "lerna": {"command": {"publish": {"assets": []}}}, "gitHead": "371eab1afca23ed0619cf7d32134b8c33d17dfe0"}