import PricingService from '../services/pricing.service.js';

const pricingService = new PricingService();

export class PricingController {
  /**
   * Get user's current credit balance and subscription info
   */
  async getUserCredits(req, res) {
    try {
      const { userId } = req.params;
      
      if (!userId) {
        return res.status(400).json({
          success: false,
          error: 'User ID is required'
        });
      }

      const credits = await pricingService.getUserCredits(userId);
      
      res.json({
        success: true,
        data: credits
      });
    } catch (error) {
      console.error('Error getting user credits:', error);
      res.status(500).json({
        success: false,
        error: error.message
      });
    }
  }

  /**
   * Create or update subscription
   */
  async createSubscription(req, res) {
    try {
      const { userId, planId, creditsAmount = 10000 } = req.body;
      
      if (!userId || !planId) {
        return res.status(400).json({
          success: false,
          error: 'User ID and Plan ID are required'
        });
      }

      const subscription = await pricingService.createSubscription(
        userId, 
        planId, 
        creditsAmount
      );
      
      res.json({
        success: true,
        data: subscription,
        message: 'Subscription created successfully'
      });
    } catch (error) {
      console.error('Error creating subscription:', error);
      res.status(500).json({
        success: false,
        error: error.message
      });
    }
  }

  /**
   * Use credits for a service
   */
  async useCredits(req, res) {
    try {
      const { userId, service, quantity, creditsPerItem = 1 } = req.body;
      
      if (!userId || !service || !quantity) {
        return res.status(400).json({
          success: false,
          error: 'User ID, service, and quantity are required'
        });
      }

      const result = await pricingService.useCredits(
        userId, 
        service, 
        quantity, 
        creditsPerItem
      );
      
      res.json({
        success: true,
        data: result,
        message: `Successfully used ${result.creditsUsed} credits`
      });
    } catch (error) {
      console.error('Error using credits:', error);
      
      // Handle specific error cases
      if (error.message.includes('Insufficient credits')) {
        return res.status(402).json({
          success: false,
          error: error.message,
          code: 'INSUFFICIENT_CREDITS'
        });
      }
      
      if (error.message.includes('expired')) {
        return res.status(402).json({
          success: false,
          error: error.message,
          code: 'SUBSCRIPTION_EXPIRED'
        });
      }
      
      res.status(500).json({
        success: false,
        error: error.message
      });
    }
  }

  /**
   * Get available pricing plans
   */
  async getPricingPlans(req, res) {
    try {
      const plans = await pricingService.getPricingPlans();
      
      res.json({
        success: true,
        data: plans
      });
    } catch (error) {
      console.error('Error getting pricing plans:', error);
      res.status(500).json({
        success: false,
        error: error.message
      });
    }
  }

  /**
   * Add bonus credits to user
   */
  async addBonusCredits(req, res) {
    try {
      const { userId, amount, description } = req.body;
      
      if (!userId || !amount) {
        return res.status(400).json({
          success: false,
          error: 'User ID and amount are required'
        });
      }

      const result = await pricingService.addBonusCredits(
        userId, 
        amount, 
        description
      );
      
      res.json({
        success: true,
        data: result,
        message: `Added ${amount} bonus credits`
      });
    } catch (error) {
      console.error('Error adding bonus credits:', error);
      res.status(500).json({
        success: false,
        error: error.message
      });
    }
  }

  /**
   * Get user's usage history
   */
  async getUserUsage(req, res) {
    try {
      const { userId } = req.params;
      const { limit = 50, offset = 0 } = req.query;
      
      if (!userId) {
        return res.status(400).json({
          success: false,
          error: 'User ID is required'
        });
      }

      const usage = await prisma.usageRecord.findMany({
        where: { userId },
        orderBy: { createdAt: 'desc' },
        take: parseInt(limit),
        skip: parseInt(offset)
      });
      
      res.json({
        success: true,
        data: usage
      });
    } catch (error) {
      console.error('Error getting user usage:', error);
      res.status(500).json({
        success: false,
        error: error.message
      });
    }
  }

  /**
   * Get user's credit transaction history
   */
  async getCreditHistory(req, res) {
    try {
      const { userId } = req.params;
      const { limit = 50, offset = 0 } = req.query;
      
      if (!userId) {
        return res.status(400).json({
          success: false,
          error: 'User ID is required'
        });
      }

      const transactions = await prisma.creditTransaction.findMany({
        where: { userId },
        orderBy: { createdAt: 'desc' },
        take: parseInt(limit),
        skip: parseInt(offset)
      });
      
      res.json({
        success: true,
        data: transactions
      });
    } catch (error) {
      console.error('Error getting credit history:', error);
      res.status(500).json({
        success: false,
        error: error.message
      });
    }
  }

  /**
   * Check if user has sufficient credits for an operation
   */
  async checkCredits(req, res) {
    try {
      const { userId, creditsNeeded } = req.body;
      
      if (!userId || !creditsNeeded) {
        return res.status(400).json({
          success: false,
          error: 'User ID and credits needed are required'
        });
      }

      const userCredits = await pricingService.getUserCredits(userId);
      
      const hasEnoughCredits = userCredits.creditsRemaining >= creditsNeeded;
      
      res.json({
        success: true,
        data: {
          hasEnoughCredits,
          creditsRemaining: userCredits.creditsRemaining,
          creditsNeeded,
          shortfall: hasEnoughCredits ? 0 : creditsNeeded - userCredits.creditsRemaining
        }
      });
    } catch (error) {
      console.error('Error checking credits:', error);
      res.status(500).json({
        success: false,
        error: error.message
      });
    }
  }
}

export default PricingController;
