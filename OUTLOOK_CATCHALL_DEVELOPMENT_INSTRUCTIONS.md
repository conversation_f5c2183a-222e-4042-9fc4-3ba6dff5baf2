# Outlook Catchall System Development Instructions

## Project Overview
Create an Outlook catchall email processing system similar to the existing email verification system (V4.2-main), but specifically designed for Outlook email processing with multiple API endpoint support.

## Current System Analysis
The existing system (V4.2-main) provides:
- Email verification using batch API processing
- Rate limiting (1000 emails per minute)
- Queue management for handling large volumes
- Apify Actor integration
- Environment variable configuration
- Error handling and retry logic

## New Requirements: Outlook Catchall System

### Core Functionality
1. **Outlook Integration**: Create a system that can process Outlook emails through catchall functionality
2. **Multiple API Endpoints**: Support processing requests through multiple API endpoints with load balancing/failover
3. **Maintain Existing Architecture**: Keep the same rate limiting, batching, and queue management structure
4. **cURL Compatibility**: Ensure the system works with cURL requests as mentioned in requirements

### Technical Specifications

#### 1. System Architecture
- **Base Framework**: Use the existing Apify Actor structure from V4.2-main
- **Language**: Node.js with ES6 modules
- **Dependencies**: Maintain axios, dotenv, and Apify dependencies
- **Rate Limiting**: Keep the 1000 requests per minute limit (adjustable via environment variables)

#### 2. Multiple API Endpoint Support
Implement the following features:

**Endpoint Management:**
- Support for multiple API endpoints in configuration
- Round-robin load balancing between endpoints
- Automatic failover to backup endpoints on failure
- Health checking for endpoint availability
- Endpoint priority/weight system

**Configuration Structure:**
```javascript
const API_ENDPOINTS = [
  {
    url: 'https://primary-outlook-api.example.com/webhook/...',
    priority: 1,
    weight: 70,
    timeout: 30000
  },
  {
    url: 'https://secondary-outlook-api.example.com/webhook/...',
    priority: 2,
    weight: 30,
    timeout: 30000
  }
];
```

#### 3. Outlook-Specific Features
- **Email Processing**: Handle Outlook-specific email formats and metadata
- **Catchall Logic**: Implement catchall email processing logic
- **Authentication**: Support for Outlook API authentication (OAuth2, API keys)
- **Email Parsing**: Extract and process email content, headers, attachments

#### 4. Enhanced Error Handling
- **Endpoint Failure Recovery**: Automatic switching to backup endpoints
- **Retry Logic**: Exponential backoff for failed requests
- **Circuit Breaker**: Temporarily disable failing endpoints
- **Logging**: Comprehensive logging for debugging and monitoring

### Implementation Tasks

#### Phase 1: Core System Setup
1. **Project Structure**:
   - Copy the existing V4.2-main structure
   - Rename to outlook-catchall-system
   - Update package.json with new project details

2. **Environment Configuration**:
   ```
   OUTLOOK_API_ENDPOINTS=comma-separated list of endpoints
   OUTLOOK_API_TIMEOUT=30000
   MAX_EMAILS_PER_MINUTE=1000
   BATCH_SIZE=1000
   ENABLE_FAILOVER=true
   HEALTH_CHECK_INTERVAL=60000
   ```

3. **Dependencies**:
   - Keep existing: apify, axios, dotenv
   - Add: node-cron (for health checks), uuid (for request tracking)

#### Phase 2: Multiple Endpoint Management
1. **Endpoint Manager Class**:
   - Parse endpoint configuration from environment variables
   - Implement load balancing algorithms
   - Health check functionality
   - Failover logic

2. **Request Router**:
   - Route requests to available endpoints
   - Handle endpoint failures gracefully
   - Maintain endpoint statistics

#### Phase 3: Outlook Integration
1. **Outlook API Client**:
   - Authentication handling
   - Email processing methods
   - Catchall-specific functionality

2. **Email Processing Pipeline**:
   - Email parsing and validation
   - Metadata extraction
   - Content processing

#### Phase 4: Enhanced Features
1. **Monitoring and Logging**:
   - Request/response logging
   - Performance metrics
   - Error tracking
   - Health status reporting

2. **Testing Suite**:
   - Unit tests for all components
   - Integration tests with mock endpoints
   - Load testing for rate limiting
   - Failover testing

### File Structure
```
outlook-catchall-system/
├── main.js                 # Main Apify Actor entry point
├── src/
│   ├── endpoint-manager.js # Multiple endpoint management
│   ├── outlook-client.js   # Outlook API integration
│   ├── email-processor.js  # Email processing logic
│   └── utils/
│       ├── rate-limiter.js # Rate limiting utilities
│       └── logger.js       # Logging utilities
├── tests/
│   ├── unit/
│   └── integration/
├── package.json
├── README.md
└── .env.example
```

### Key Implementation Notes

1. **Backward Compatibility**: Ensure the system can work with existing cURL commands by maintaining similar input/output formats

2. **Scalability**: Design the system to handle high volumes of Outlook emails efficiently

3. **Security**: Implement proper authentication and secure handling of email data

4. **Documentation**: Provide comprehensive documentation for setup, configuration, and usage

5. **Testing**: Include thorough testing for all failure scenarios and edge cases

### Deliverables
1. Complete working Outlook catchall system
2. Comprehensive documentation
3. Test suite with >90% coverage
4. Configuration examples
5. Deployment instructions
6. Performance benchmarks

### Timeline Estimate
- Phase 1: 2-3 days
- Phase 2: 3-4 days  
- Phase 3: 4-5 days
- Phase 4: 2-3 days
- Testing & Documentation: 2-3 days

**Total Estimated Time: 13-18 days**

### Success Criteria
- System processes Outlook emails through multiple endpoints
- Maintains 99.9% uptime with proper failover
- Handles rate limiting effectively
- Provides comprehensive logging and monitoring
- Passes all test suites
- Compatible with existing cURL workflows
