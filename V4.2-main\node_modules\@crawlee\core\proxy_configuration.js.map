{"version": 3, "file": "proxy_configuration.js", "sourceRoot": "", "sources": ["../src/proxy_configuration.ts"], "names": [], "mappings": ";;;;AACA,oDAAoB;AAEpB,6DAA6B;AAC7B,gDAAwD;AAmHxD;;;;GAIG;AACH,MAAM,gBAAgB;IAIlB,YAAY,eAAoC;QAHxC;;;;;WAAoB;QACpB;;;;;WAAoB;QAGxB,IAAI,CAAC,SAAS,GAAG,eAAe,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;QAC9C,IAAI,CAAC,WAAW,GAAG,CAAC,CAAC;IACzB,CAAC;IAED;;OAEG;IACK,WAAW;QACf,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE;YAC5B,IAAI,IAAI,CAAC,WAAW,KAAK,CAAC;gBAAE,OAAO;YACnC,IAAI,CAAC,GAAG,CAAC;gBAAE,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,CAAC;QACnC,CAAC,CAAC,CAAC;QAEH,MAAM,IAAI,GAAG,IAAI,CAAC,WAAW,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,WAAW,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC;QACpF,MAAM,KAAK,GAAG,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,WAAW,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC;QAE7G,IAAI,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,WAAW,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,KAAK,CAAC,EAAE,CAAC;YAC3D,IAAI,CAAC,WAAW,GAAG,IAAI,IAAI,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,WAAW,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,WAAW,GAAG,CAAC,CAAC;QACnF,CAAC;aAAM,IAAI,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,WAAW,CAAC,KAAK,IAAI,EAAE,CAAC;YACnD,IAAI,CAAC,WAAW,EAAE,CAAC;QACvB,CAAC;IACL,CAAC;IAED;;;;;OAKG;IACH,QAAQ,CAAC,IAAY;QACjB,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC;IAC/B,CAAC;IAED;;;OAGG;IACH,WAAW;QACP,IAAI,CAAC,WAAW,EAAE,CAAC;QACnB,OAAO,IAAI,CAAC,WAAW,CAAC;IAC5B,CAAC;CACJ;AAED;;;;;;;;;;;;;;;;;;;;;;;;;;;GA2BG;AACH,MAAa,kBAAkB;IAU3B;;;;;;;;;;;;;;;;;;;OAmBG;IACH,YAAY,UAAqC,EAAE;QA7BnD;;;;mBAAmB,KAAK;WAAC;QACf;;;;mBAAqB,CAAC;WAAC;QACvB;;;;;WAAqB;QACrB;;;;;WAAsC;QACtC;;;;mBAAgB,IAAI,GAAG,EAAkB;WAAC;QAC1C;;;;;WAA4C;QAC5C;;;;mBAAM,aAAG,CAAC,KAAK,CAAC,EAAE,MAAM,EAAE,oBAAoB,EAAE,CAAC;WAAC;QAClD;;;;mBAAc,IAAI,GAAG,EAA4B;WAAC;QAuBxD,MAAM,EAAE,gBAAgB,EAAE,GAAG,IAAI,EAAE,GAAG,OAAqB,CAAC;QAC5D,IAAA,YAAE,EACE,IAAI,EACJ,YAAE,CAAC,MAAM,CAAC,UAAU,CAAC;YACjB,SAAS,EAAE,YAAE,CAAC,QAAQ,CAAC,KAAK,CAAC,QAAQ,CAAC,MAAM,CAAC,YAAE,CAAC,MAAM,CAAC,GAAG,CAAC;YAC3D,cAAc,EAAE,YAAE,CAAC,QAAQ,CAAC,QAAQ;YACpC,eAAe,EAAE,YAAE,CAAC,QAAQ,CAAC,KAAK,CAAC,QAAQ,CAAC,MAAM,CAC9C,YAAE,CAAC,KAAK,CAAC,QAAQ,CAAC,MAAM,CAAC,YAAE,CAAC,GAAG,CAAC,YAAE,CAAC,MAAM,CAAC,GAAG,EAAE,YAAE,CAAC,IAAI,CAAC,CAAC,CAC3D;SACJ,CAAC,CACL,CAAC;QAEF,MAAM,EAAE,SAAS,EAAE,cAAc,EAAE,eAAe,EAAE,GAAG,OAAO,CAAC;QAE/D,IAAI,CAAC,SAAS,EAAE,cAAc,EAAE,eAAe,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,MAAM,GAAG,CAAC;YACxE,IAAI,CAAC,gCAAgC,EAAE,CAAC;QAC5C,IAAI,CAAC,SAAS,IAAI,CAAC,cAAc,IAAI,gBAAgB;YAAE,IAAI,CAAC,uBAAuB,EAAE,CAAC;QAEtF,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;QAC3B,IAAI,CAAC,cAAc,GAAG,cAAc,CAAC;QACrC,IAAI,CAAC,eAAe,GAAG,eAAe,CAAC;IAC3C,CAAC;IAED;;;;;;;;;;;;;;;;OAgBG;IACH,KAAK,CAAC,YAAY,CAAC,SAA2B,EAAE,OAA4B;QACxE,IAAI,OAAO,SAAS,KAAK,QAAQ;YAAE,SAAS,GAAG,GAAG,SAAS,EAAE,CAAC;QAE9D,IAAI,GAAuB,CAAC;QAC5B,IAAI,IAAwB,CAAC;QAC7B,IAAI,IAAI,CAAC,eAAe,EAAE,CAAC;YACvB,MAAM,EAAE,QAAQ,EAAE,SAAS,EAAE,GAAG,IAAI,CAAC,gBAAgB,CAAC,SAAS,IAAI,IAAA,gCAAoB,EAAC,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC;YACrG,GAAG,GAAG,QAAQ,IAAI,SAAS,CAAC;YAC5B,IAAI,GAAG,SAAS,CAAC;QACrB,CAAC;aAAM,CAAC;YACJ,GAAG,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;QAChD,CAAC;QAED,IAAI,CAAC,GAAG;YAAE,OAAO,SAAS,CAAC;QAE3B,MAAM,EAAE,QAAQ,EAAE,QAAQ,EAAE,IAAI,EAAE,QAAQ,EAAE,GAAG,IAAI,GAAG,CAAC,GAAG,CAAC,CAAC;QAE5D,OAAO;YACH,SAAS;YACT,GAAG;YACH,QAAQ,EAAE,kBAAkB,CAAC,QAAQ,CAAC;YACtC,QAAQ,EAAE,kBAAkB,CAAC,QAAQ,CAAC;YACtC,QAAQ;YACR,IAAI,EAAE,IAAK;YACX,SAAS,EAAE,IAAI;SAClB,CAAC;IACN,CAAC;IAED;;;;;OAKG;IACO,gBAAgB,CAAC,UAAkB,EAAE,OAA4B;QACvE,IAAI,CAAC,IAAI,CAAC,eAAe;YAAE,MAAM,IAAI,KAAK,CAAC,+BAA+B,CAAC,CAAC;QAE5E,IAAI,CAAC,OAAO,IAAI,CAAC,CAAC,OAAO,EAAE,OAAO,IAAI,OAAO,EAAE,SAAS,KAAK,SAAS,CAAC,EAAE,CAAC;YACtE,MAAM,YAAY,GAAG,IAAI,CAAC,eAAe,CAAC,IAAI,EAAE,CAAC;YACjD,OAAO;gBACH,QAAQ,EAAE,YAAY,CAAC,IAAI,CAAC,kBAAkB,EAAE,GAAG,YAAY,CAAC,MAAM,CAAC;aAC1E,CAAC;QACN,CAAC;QAED,IAAI,cAAc,GAAG,OAAO,CAAC,SAAU,CAAC;QAExC,IAAI,OAAO,cAAc,KAAK,QAAQ,EAAE,CAAC;YACrC,cAAc,GAAG,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,OAAQ,CAAE,CAAC;QAC9D,CAAC;QAED,MAAM,SAAS,GAAG,IAAI,CAAC,eAAgB,CAAC,cAAc,CAAC,CAAC;QAExD,OAAO;YACH,QAAQ,EAAE,SAAS,CAAC,IAAI,CAAC,kBAAkB,EAAE,GAAG,SAAS,CAAC,MAAM,CAAC;YACjE,SAAS,EAAE,cAAc;SAC5B,CAAC;IACN,CAAC;IAED;;;;OAIG;IACO,gBAAgB,CAAC,OAAgB;;QACvC,IAAI,CAAC,IAAI,CAAC,eAAe;YAAE,OAAO,IAAI,CAAC;QAEvC,MAAM,MAAM,GAAG,IAAI,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,QAAQ,CAAC;QAC7C,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC;YAChC,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,MAAM,EAAE,IAAI,gBAAgB,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC;QAC7E,CAAC;QAED,MAAA,OAAO,CAAC,QAAQ,EAAC,SAAS,QAAT,SAAS,GAAK,EAAE,EAAC;QAElC,MAAM,OAAO,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,MAAM,CAAE,CAAC;QAE9C,IAAI,OAAO,OAAO,CAAC,QAAQ,CAAC,SAAS,CAAC,aAAa,KAAK,QAAQ,EAAE,CAAC;YAC/D,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC,QAAQ,CAAC,SAAS,CAAC,aAAa,CAAC,CAAC;QAC/D,CAAC;QAED,MAAM,cAAc,GAAG,OAAO,CAAC,WAAW,EAAE,CAAC;QAE7C,IACI,OAAO,OAAO,CAAC,QAAQ,CAAC,SAAS,CAAC,aAAa,KAAK,QAAQ;YAC5D,OAAO,CAAC,QAAQ,CAAC,SAAS,CAAC,aAAa,KAAK,cAAc,EAC7D,CAAC;YACC,aAAG,CAAC,KAAK,CACL,mCAAmC,MAAM,UAAU,OAAO,CAAC,QAAQ,CAAC,SAAS,CAAC,aAAa,OAAO,cAAc,GAAG,CACtH,CAAC;QACN,CAAC;QAED,OAAO,CAAC,QAAQ,CAAC,SAAS,CAAC,aAAa,GAAG,cAAc,CAAC;QAC1D,OAAO,CAAC,QAAQ,CAAC,SAAS,CAAC,SAAS,GAAG,IAAI,CAAC;QAE5C,OAAO,cAAc,CAAC;IAC1B,CAAC;IAED;;;;;;;;;;;;OAYG;IACH,KAAK,CAAC,MAAM,CAAC,SAA2B,EAAE,OAA4B;QAClE,IAAI,OAAO,SAAS,KAAK,QAAQ;YAAE,SAAS,GAAG,GAAG,SAAS,EAAE,CAAC;QAE9D,IAAI,IAAI,CAAC,cAAc,EAAE,CAAC;YACtB,OAAO,CAAC,MAAM,IAAI,CAAC,mBAAmB,CAAC,SAAS,EAAE,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,CAAC,CAAC,IAAI,SAAS,CAAC;QACnG,CAAC;QAED,IAAI,IAAI,CAAC,eAAe,EAAE,CAAC;YACvB,OAAO,IAAI,CAAC,gBAAgB,CAAC,SAAS,IAAI,IAAA,gCAAoB,EAAC,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC,QAAQ,IAAI,SAAS,CAAC;QACtG,CAAC;QAED,OAAO,IAAI,CAAC,gBAAgB,CAAC,SAAS,CAAC,CAAC;IAC5C,CAAC;IAED;;OAEG;IACO,gBAAgB,CAAC,SAAkB;QACzC,IAAI,cAAsB,CAAC;QAE3B,IAAI,CAAC,SAAS,EAAE,CAAC;YACb,OAAO,IAAI,CAAC,SAAU,CAAC,IAAI,CAAC,kBAAkB,EAAE,GAAG,IAAI,CAAC,SAAU,CAAC,MAAM,CAAC,CAAC;QAC/E,CAAC;QAED,IAAI,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,SAAS,CAAC,EAAE,CAAC;YACpC,cAAc,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,SAAS,CAAE,CAAC;QACxD,CAAC;aAAM,CAAC;YACJ,cAAc,GAAG,IAAI,CAAC,SAAU,CAAC,IAAI,CAAC,kBAAkB,EAAE,GAAG,IAAI,CAAC,SAAU,CAAC,MAAM,CAAC,CAAC;YACrF,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,SAAS,EAAE,cAAc,CAAC,CAAC;QACtD,CAAC;QAED,OAAO,cAAc,CAAC;IAC1B,CAAC;IAED;;OAEG;IACO,KAAK,CAAC,mBAAmB,CAAC,SAAkB,EAAE,OAA+B;QACnF,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,cAAe,CAAC,SAAU,EAAE,OAAO,CAAC,CAAC;QACjE,IAAI,CAAC;YACD,IAAI,QAAQ,EAAE,CAAC;gBACX,IAAI,GAAG,CAAC,QAAQ,CAAC,CAAC,CAAC,6BAA6B;YACpD,CAAC;YACD,OAAO,QAAQ,CAAC;QACpB,CAAC;QAAC,OAAO,GAAG,EAAE,CAAC;YACX,MAAM,IAAI,KAAK,CACX,mEAAoE,GAAa,CAAC,OAAO,EAAE,CAC9F,CAAC;QACN,CAAC;IACL,CAAC;IAES,gCAAgC;QACtC,MAAM,IAAI,KAAK,CACX,6GAA6G,CAChH,CAAC;IACN,CAAC;IAES,uBAAuB;QAC7B,MAAM,IAAI,KAAK,CAAC,8EAA8E,CAAC,CAAC;IACpG,CAAC;CACJ;AAhPD,gDAgPC"}