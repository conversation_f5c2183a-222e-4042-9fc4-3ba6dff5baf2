{"version": 3, "file": "key_value_store.js", "sourceRoot": "", "sources": ["../../src/resource_clients/key_value_store.ts"], "names": [], "mappings": ";;;;AAEA,oDAAoB;AAIpB,6DAA6B;AAI7B,6DAKiC;AAEjC,oCAA8G;AAE9G,MAAa,mBAAoB,SAAQ,gCAAc;IACnD;;OAEG;IACH,YAAY,OAAoC;QAC5C,KAAK,CAAC;YACF,YAAY,EAAE,kBAAkB;YAChC,GAAG,OAAO;SACb,CAAC,CAAC;IACP,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,GAAG;QACL,OAAO,IAAI,CAAC,IAAI,CAAC,EAAE,EAAE,sCAAoB,CAAC,CAAC;IAC/C,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,MAAM,CAAC,SAAsC;QAC/C,IAAA,YAAE,EAAC,SAAS,EAAE,YAAE,CAAC,MAAM,CAAC,CAAC;QAEzB,OAAO,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE,wCAAsB,CAAC,CAAC;IAC3D,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,MAAM;QACR,OAAO,IAAI,CAAC,OAAO,CAAC,sCAAoB,CAAC,CAAC;IAC9C,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,QAAQ,CAAC,UAAyC,EAAE;QACtD,IAAA,YAAE,EACE,OAAO,EACP,YAAE,CAAC,MAAM,CAAC,UAAU,CAAC;YACjB,KAAK,EAAE,YAAE,CAAC,QAAQ,CAAC,MAAM;YACzB,iBAAiB,EAAE,YAAE,CAAC,QAAQ,CAAC,MAAM;YACrC,UAAU,EAAE,YAAE,CAAC,QAAQ,CAAC,MAAM;YAC9B,MAAM,EAAE,YAAE,CAAC,QAAQ,CAAC,MAAM;SAC7B,CAAC,CACL,CAAC;QAEF,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC;YACxC,GAAG,EAAE,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC;YACtB,MAAM,EAAE,KAAK;YACb,MAAM,EAAE,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC;YAC7B,OAAO,EAAE,uCAAqB;SACjC,CAAC,CAAC;QAEH,OAAO,IAAA,YAAI,EAAC,IAAA,uBAAe,EAAC,IAAA,iBAAS,EAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;IAC3D,CAAC;IAED;;;;;;OAMG;IACH,KAAK,CAAC,YAAY,CAAC,GAAW;QAC1B,MAAM,WAAW,GAA4B;YACzC,GAAG,EAAE,IAAI,CAAC,IAAI,CAAC,WAAW,GAAG,EAAE,CAAC;YAChC,MAAM,EAAE,MAAM;YACd,MAAM,EAAE,IAAI,CAAC,OAAO,EAAE;SACzB,CAAC;QAEF,IAAI,CAAC;YACD,MAAM,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;YACxC,OAAO,IAAI,CAAC;QAChB,CAAC;QAAC,OAAO,GAAG,EAAE,CAAC;YACX,IAAA,4BAAoB,EAAC,GAAoB,CAAC,CAAC;QAC/C,CAAC;QAED,OAAO,KAAK,CAAC;IACjB,CAAC;IAkBD,KAAK,CAAC,SAAS,CACX,GAAW,EACX,UAA0C,EAAE;QAE5C,IAAA,YAAE,EAAC,GAAG,EAAE,YAAE,CAAC,MAAM,CAAC,CAAC;QACnB,IAAA,YAAE,EACE,OAAO,EACP,YAAE,CAAC,MAAM,CAAC,UAAU,CAAC;YACjB,MAAM,EAAE,YAAE,CAAC,QAAQ,CAAC,OAAO;YAC3B,MAAM,EAAE,YAAE,CAAC,QAAQ,CAAC,OAAO;YAC3B,eAAe,EAAE,YAAE,CAAC,QAAQ,CAAC,OAAO;SACvC,CAAC,CACL,CAAC;QAEF,IAAI,OAAO,CAAC,MAAM,IAAI,CAAC,IAAA,cAAM,GAAE,EAAE,CAAC;YAC9B,MAAM,IAAI,KAAK,CAAC,4DAA4D,CAAC,CAAC;QAClF,CAAC;QAED,IAAI,iBAAiB,IAAI,OAAO,EAAE,CAAC;YAC/B,aAAG,CAAC,UAAU,CACV,4DAA4D;gBACxD,sEAAsE,CAC7E,CAAC;QACN,CAAC;QAED,MAAM,WAAW,GAA4B;YACzC,GAAG,EAAE,IAAI,CAAC,IAAI,CAAC,WAAW,GAAG,EAAE,CAAC;YAChC,MAAM,EAAE,KAAK;YACb,MAAM,EAAE,IAAI,CAAC,OAAO,EAAE;YACtB,OAAO,EAAE,wCAAsB;SAClC,CAAC;QAEF,IAAI,OAAO,CAAC,MAAM;YAAE,WAAW,CAAC,WAAW,GAAG,IAAI,CAAC;QACnD,IAAI,OAAO,CAAC,MAAM;YAAE,WAAW,CAAC,YAAY,GAAG,QAAQ,CAAC;QAExD,IAAI,CAAC;YACD,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;YACzD,OAAO;gBACH,GAAG;gBACH,KAAK,EAAE,QAAQ,CAAC,IAAI;gBACpB,WAAW,EAAE,QAAQ,CAAC,OAAO,CAAC,cAAc,CAAC;aAChD,CAAC;QACN,CAAC;QAAC,OAAO,GAAG,EAAE,CAAC;YACX,IAAA,4BAAoB,EAAC,GAAoB,CAAC,CAAC;QAC/C,CAAC;QAED,OAAO,SAAS,CAAC;IACrB,CAAC;IAED;;;;;;;;;;OAUG;IACH,KAAK,CAAC,SAAS,CAAC,MAAsC,EAAE,UAAsC,EAAE;QAC5F,IAAA,YAAE,EACE,MAAM,EACN,YAAE,CAAC,MAAM,CAAC,UAAU,CAAC;YACjB,GAAG,EAAE,YAAE,CAAC,MAAM;YACd,KAAK,EAAE,YAAE,CAAC,GAAG,CAAC,YAAE,CAAC,IAAI,EAAE,YAAE,CAAC,MAAM,EAAE,YAAE,CAAC,MAAM,EAAE,YAAE,CAAC,MAAM,EAAE,YAAE,CAAC,OAAO,CAAC;YACnE,WAAW,EAAE,YAAE,CAAC,QAAQ,CAAC,MAAM,CAAC,QAAQ;SAC3C,CAAC,CACL,CAAC;QAEF,IAAA,YAAE,EACE,OAAO,EACP,YAAE,CAAC,MAAM,CAAC,UAAU,CAAC;YACjB,WAAW,EAAE,YAAE,CAAC,QAAQ,CAAC,MAAM;YAC/B,kBAAkB,EAAE,YAAE,CAAC,QAAQ,CAAC,OAAO;SAC1C,CAAC,CACL,CAAC;QAEF,MAAM,EAAE,GAAG,EAAE,GAAG,MAAM,CAAC;QACvB,IAAI,EAAE,KAAK,EAAE,WAAW,EAAE,GAAG,MAAM,CAAC;QACpC,MAAM,EAAE,WAAW,EAAE,kBAAkB,EAAE,GAAG,OAAO,CAAC;QAEpD,MAAM,qBAAqB,GAAG,IAAA,gBAAQ,EAAC,KAAK,CAAC,IAAI,IAAA,gBAAQ,EAAC,KAAK,CAAC,CAAC;QACjE,iEAAiE;QACjE,IAAI,CAAC,WAAW,EAAE,CAAC;YACf,IAAI,qBAAqB;gBAAE,WAAW,GAAG,0BAA0B,CAAC;iBAC/D,IAAI,OAAO,KAAK,KAAK,QAAQ;gBAAE,WAAW,GAAG,2BAA2B,CAAC;;gBACzE,WAAW,GAAG,iCAAiC,CAAC;QACzD,CAAC;QAED,MAAM,iBAAiB,GAAG,oBAAoB,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;QACjE,IAAI,iBAAiB,IAAI,CAAC,qBAAqB,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE,CAAC;YAC3E,IAAI,CAAC;gBACD,KAAK,GAAG,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;YAC3C,CAAC;YAAC,OAAO,GAAG,EAAE,CAAC;gBACX,MAAM,GAAG,GAAG,8FAA+F,GAAa,CAAC,OAAO,EAAE,CAAC;gBACnI,MAAM,IAAI,KAAK,CAAC,GAAG,CAAC,CAAC;YACzB,CAAC;QACL,CAAC;QAED,MAAM,UAAU,GAAuB;YACnC,GAAG,EAAE,IAAI,CAAC,IAAI,CAAC,WAAW,GAAG,EAAE,CAAC;YAChC,MAAM,EAAE,KAAK;YACb,MAAM,EAAE,IAAI,CAAC,OAAO,EAAE;YACtB,IAAI,EAAE,KAAK;YACX,OAAO,EAAE,WAAW,CAAC,CAAC,CAAC,EAAE,cAAc,EAAE,WAAW,EAAE,CAAC,CAAC,CAAC,SAAS;YAClE,kBAAkB;YAClB,OAAO,EAAE,WAAW,KAAK,SAAS,CAAC,CAAC,CAAC,WAAW,GAAG,IAAI,CAAC,CAAC,CAAC,wCAAsB;SACnF,CAAC;QAEF,MAAM,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;IAC3C,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,YAAY,CAAC,GAAW;QAC1B,IAAA,YAAE,EAAC,GAAG,EAAE,YAAE,CAAC,MAAM,CAAC,CAAC;QAEnB,MAAM,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC;YACvB,GAAG,EAAE,IAAI,CAAC,IAAI,CAAC,WAAW,GAAG,EAAE,CAAC;YAChC,MAAM,EAAE,QAAQ;YAChB,MAAM,EAAE,IAAI,CAAC,OAAO,EAAE;YACtB,OAAO,EAAE,sCAAoB;SAChC,CAAC,CAAC;IACP,CAAC;CACJ;AAhOD,kDAgOC"}