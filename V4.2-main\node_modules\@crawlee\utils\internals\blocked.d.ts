export declare const CLOUDFLARE_RETRY_CSS_SELECTORS: string[];
/**
 * CSS selectors for elements that should trigger a retry, as the crawler is likely getting blocked.
 */
export declare const RETRY_CSS_SELECTORS: string[];
/**
 * Content of proxy errors that should trigger a retry, as the proxy is likely getting blocked / is malfunctioning.
 */
export declare const ROTATE_PROXY_ERRORS: string[];
//# sourceMappingURL=blocked.d.ts.map