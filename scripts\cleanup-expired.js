import PricingService from '../src/services/pricing.service.js';

const pricingService = new PricingService();

/**
 * Standalone script to clean up expired subscriptions
 * Can be run manually or as a cron job
 */
async function cleanupExpiredSubscriptions() {
  console.log('🧹 Starting cleanup of expired subscriptions...');
  console.log(`⏰ Current time: ${new Date().toISOString()}`);

  try {
    const cleanedUp = await pricingService.cleanupExpiredSubscriptions();
    
    console.log(`✅ Cleanup completed successfully!`);
    console.log(`📊 Processed ${cleanedUp} expired subscriptions`);
    
    if (cleanedUp > 0) {
      console.log(`💳 ${cleanedUp} subscriptions were expired and credits were cleared`);
    } else {
      console.log('🎉 No expired subscriptions found - all good!');
    }
    
    process.exit(0);
  } catch (error) {
    console.error('❌ Cleanup failed:', error);
    process.exit(1);
  }
}

// Run cleanup if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
  cleanupExpiredSubscriptions();
}

export { cleanupExpiredSubscriptions };
