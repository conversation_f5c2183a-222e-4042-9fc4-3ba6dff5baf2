{"version": 3, "file": "key-value-store.d.ts", "sourceRoot": "", "sources": ["../../src/resource-clients/key-value-store.ts"], "names": [], "mappings": "AAKA,OAAO,KAAK,KAAK,OAAO,MAAM,gBAAgB,CAAC;AAW/C,OAAO,KAAK,EAAE,aAAa,EAAE,MAAM,UAAU,CAAC;AAE9C,OAAO,EAAE,UAAU,EAAE,MAAM,sBAAsB,CAAC;AAIlD,MAAM,WAAW,0BAA0B;IACvC,IAAI,CAAC,EAAE,MAAM,CAAC;IACd,EAAE,CAAC,EAAE,MAAM,CAAC;IACZ,oBAAoB,EAAE,MAAM,CAAC;IAC7B,MAAM,EAAE,aAAa,CAAC;CACzB;AAED,MAAM,WAAW,iBAAiB;IAC9B,GAAG,EAAE,MAAM,CAAC;IACZ,KAAK,EAAE,MAAM,GAAG,MAAM,CAAC;IACvB,WAAW,CAAC,EAAE,MAAM,CAAC;IACrB,SAAS,EAAE,MAAM,CAAC;CACrB;AAED,qBAAa,mBAAoB,SAAQ,UAAU;IAC/C,IAAI,CAAC,EAAE,MAAM,CAAC;IACd,SAAS,OAAc;IACvB,UAAU,OAAc;IACxB,UAAU,OAAc;IACxB,sBAAsB,EAAE,MAAM,CAAC;IAE/B,OAAO,CAAC,QAAQ,CAAC,eAAe,CAA+D;IAC/F,OAAO,CAAC,QAAQ,CAAC,MAAM,CAAgB;gBAE3B,OAAO,EAAE,0BAA0B;IAOzC,GAAG,IAAI,OAAO,CAAC,OAAO,CAAC,iBAAiB,GAAG,SAAS,CAAC;IAWrD,MAAM,CAAC,SAAS,GAAE,OAAO,CAAC,gCAAqC,GAAG,OAAO,CAAC,OAAO,CAAC,iBAAiB,CAAC;IA6CpG,MAAM,IAAI,OAAO,CAAC,IAAI,CAAC;IAWvB,QAAQ,CAAC,OAAO,GAAE,OAAO,CAAC,8BAAmC,GAAG,OAAO,CAAC,OAAO,CAAC,2BAA2B,CAAC;IAkElH;;;;;OAKG;IACG,YAAY,CAAC,GAAG,EAAE,MAAM,GAAG,OAAO,CAAC,OAAO,CAAC;IAa3C,SAAS,CACX,GAAG,EAAE,MAAM,EACX,OAAO,GAAE,OAAO,CAAC,mCAAwC,GAC1D,OAAO,CAAC,OAAO,CAAC,mBAAmB,GAAG,SAAS,CAAC;IA4C7C,SAAS,CAAC,MAAM,EAAE,OAAO,CAAC,mBAAmB,GAAG,OAAO,CAAC,IAAI,CAAC;IA+E7D,YAAY,CAAC,GAAG,EAAE,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC;IAmB9C,mBAAmB,IAAI,OAAO,CAAC,iBAAiB;IAWhD,OAAO,CAAC,gBAAgB;CAkB3B"}