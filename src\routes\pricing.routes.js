import express from 'express';
import PricingController from '../controllers/pricing.controller.js';

const router = express.Router();
const pricingController = new PricingController();

// Get user's current credits and subscription info
router.get('/users/:userId/credits', pricingController.getUserCredits);

// Create or update subscription
router.post('/subscriptions', pricingController.createSubscription);

// Use credits for a service
router.post('/credits/use', pricingController.useCredits);

// Get available pricing plans
router.get('/plans', pricingController.getPricingPlans);

// Add bonus credits (admin only)
router.post('/credits/bonus', pricingController.addBonusCredits);

// Get user's usage history
router.get('/users/:userId/usage', pricingController.getUserUsage);

// Get user's credit transaction history
router.get('/users/:userId/transactions', pricingController.getCreditHistory);

// Check if user has sufficient credits
router.post('/credits/check', pricingController.checkCredits);

export default router;
