{"version": 3, "file": "ps-tree.js", "sourceRoot": "", "sources": ["../../../src/internals/systemInfoV2/ps-tree.ts"], "names": [], "mappings": ";;AA0BA,wBA+GC;;AAxID,2DAA2C;AAC3C,gEAA0C;AAe1C;;;;;;;;GAQG;AACI,KAAK,UAAU,MAAM,CAAC,GAAoB,EAAE,WAAW,GAAG,KAAK;IAClE,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;QACnC,IAAI,OAAO,GAAG,KAAK,QAAQ,EAAE,CAAC;YAC1B,GAAG,GAAG,GAAG,CAAC,QAAQ,EAAE,CAAC;QACzB,CAAC;QAED,IAAI,aAA2B,CAAC;QAChC,IAAI,OAAO,CAAC,QAAQ,KAAK,OAAO,EAAE,CAAC;YAC/B,aAAa,GAAG,IAAA,0BAAK,EAAC,YAAY,EAAE;gBAChC,YAAY;gBACZ,UAAU;gBACV,4FAA4F;aAC/F,CAAC,CAAC;QACP,CAAC;aAAM,CAAC;YACJ,aAAa,GAAG,IAAA,0BAAK,EAAC,IAAI,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,wBAAwB,CAAC,CAAC,CAAC;QACxE,CAAC;QAED,aAAa,CAAC,EAAE,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;QAElC,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE,CAAC;YACxB,MAAM,CAAC,IAAI,KAAK,CAAC,8BAA8B,CAAC,CAAC,CAAC;YAClD,OAAO;QACX,CAAC;QAED,8DAA8D;QAC9D,MAAM,EAAE,GAAG,QAAQ,CAAC,eAAe,CAAC;YAChC,KAAK,EAAE,aAAa,CAAC,MAAM;SAC9B,CAAC,CAAC;QAEH,MAAM,IAAI,GAAkB,EAAE,CAAC;QAC/B,IAAI,OAAO,GAAoB,IAAI,CAAC;QAEpC,EAAE,CAAC,EAAE,CAAC,MAAM,EAAE,CAAC,IAAY,EAAE,EAAE;YAC3B,MAAM,OAAO,GAAG,IAAI,CAAC,IAAI,EAAE,CAAC;YAC5B,IAAI,OAAO,KAAK,EAAE,EAAE,CAAC;gBACjB,OAAO,CAAC,oBAAoB;YAChC,CAAC;YAED,4DAA4D;YAC5D,MAAM,MAAM,GAAG,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;YACpC,IAAI,OAAO,KAAK,IAAI,IAAI,MAAM,CAAC,KAAK,CAAC,CAAC,KAAK,EAAE,EAAE,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC;gBAClE,OAAO;YACX,CAAC;YAED,2DAA2D;YAC3D,IAAI,CAAC,OAAO,EAAE,CAAC;gBACX,OAAO,GAAG,MAAM,CAAC,GAAG,CAAC,eAAe,CAAC,CAAC;gBACtC,OAAO;YACX,CAAC;YAED,4CAA4C;YAC5C,MAAM,OAAO,GAAG,MAAM,CAAC,KAAK,EAAE,CAAC;YAE/B,gCAAgC;YAChC,MAAM,GAAG,GAAyB,EAAE,CAAC;YACrC,MAAM,IAAI,GAAG,OAAO,CAAC,KAAK,EAAE,CAAC;YAC7B,qEAAqE;YACrE,gGAAgG;YAChG,KAAK,MAAM,CAAC,KAAK,EAAE,MAAM,CAAC,IAAI,IAAI,CAAC,OAAO,EAAE,EAAE,CAAC;gBAC3C,IAAI,KAAa,CAAC;gBAClB,IAAI,KAAK,KAAK,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;oBAC5B,KAAK,GAAG,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;gBAC9B,CAAC;qBAAM,CAAC;oBACJ,KAAK,GAAG,OAAO,CAAC,KAAK,EAAG,CAAC;gBAC7B,CAAC;gBAED,IAAI,MAAM,KAAK,KAAK,EAAE,CAAC;oBACnB,GAAG,CAAC,MAAM,CAAC,GAAG,MAAM,CAAC,QAAQ,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC;oBACzC,IAAI,OAAO,CAAC,QAAQ,KAAK,OAAO,EAAE,CAAC;wBAC/B,sDAAsD;wBACtD,GAAG,CAAC,MAAM,CAAC,IAAI,IAAI,CAAC;oBACxB,CAAC;gBACL,CAAC;qBAAM,CAAC;oBACJ,GAAG,CAAC,MAA+B,CAAC,GAAG,KAAK,CAAC;gBACjD,CAAC;YACL,CAAC;YAED,4DAA4D;YAC5D,IAAI,OAAO,CAAC,QAAQ,KAAK,OAAO,EAAE,CAAC;gBAC/B,GAAG,CAAC,IAAI,GAAG,IAAI,CAAC;YACpB,CAAC;YAED,IAAI,CAAC,IAAI,CAAC,GAAkB,CAAC,CAAC;QAClC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,EAAE,CAAC,OAAO,EAAE,GAAG,EAAE;YAChB,MAAM,OAAO,GAA+B,EAAE,CAAC;YAC/C,MAAM,QAAQ,GAAkB,EAAE,CAAC;YAEnC,8BAA8B;YAC9B,OAAO,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC;YAEpB,qCAAqC;YACrC,IAAI,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,EAAE;gBAClB,+EAA+E;gBAC/E,IAAI,IAAI,CAAC,OAAO,KAAK,IAAI,IAAI,IAAI,CAAC,OAAO,KAAK,gBAAgB,EAAE,CAAC;oBAC7D,OAAO;gBACX,CAAC;gBACD,IAAI,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;oBACrB,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC;oBACzB,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBACxB,CAAC;qBAAM,IAAI,WAAW,IAAI,GAAG,KAAK,IAAI,CAAC,GAAG,EAAE,CAAC;oBACzC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBACxB,CAAC;YACL,CAAC,CAAC,CAAC;YACH,OAAO,CAAC,QAAQ,CAAC,CAAC;QACtB,CAAC,CAAC,CAAC;QAEH,+CAA+C;QAC/C,aAAa,CAAC,MAAM,CAAC,EAAE,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;IAC7C,CAAC,CAAC,CAAC;AACP,CAAC;AAED;;;;;;;;;;;GAWG;AACH,SAAS,eAAe,CAAC,GAAW;IAChC,IAAI,OAAO,CAAC,QAAQ,KAAK,OAAO,EAAE,CAAC;QAC/B,+CAA+C;QAC/C,IAAI,GAAG,KAAK,MAAM;YAAE,OAAO,SAAS,CAAC;QACrC,OAAO,GAAG,CAAC;IACf,CAAC;IAED,QAAQ,GAAG,EAAE,CAAC;QACV,KAAK,MAAM;YACP,OAAO,SAAS,CAAC;QACrB,KAAK,iBAAiB;YAClB,OAAO,MAAM,CAAC;QAClB,KAAK,WAAW;YACZ,OAAO,KAAK,CAAC;QACjB,KAAK,QAAQ;YACT,OAAO,MAAM,CAAC;QAClB,KAAK,gBAAgB;YACjB,OAAO,KAAK,CAAC;QACjB;YACI,MAAM,IAAI,KAAK,CAAC,mCAAmC,GAAG,EAAE,CAAC,CAAC;IAClE,CAAC;AACL,CAAC"}