{"name": "apify-client", "version": "2.12.6", "description": "Apify API client for JavaScript", "main": "dist/index.js", "module": "dist/index.mjs", "types": "dist/index.d.ts", "browser": "dist/bundle.js", "unpkg": "dist/bundle.js", "exports": {"./package.json": "./package.json", ".": {"import": "./dist/index.mjs", "require": "./dist/index.js", "types": "./dist/index.d.ts", "browser": "./dist/bundle.js"}}, "keywords": ["apify", "api", "apifier", "crawler", "scraper"], "author": {"name": "Apify", "email": "<EMAIL>", "url": "https://apify.com"}, "contributors": ["<PERSON> <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>", "<PERSON><PERSON><PERSON> <<EMAIL>>"], "license": "Apache-2.0", "repository": {"type": "git", "url": "git+https://github.com/apify/apify-client-js"}, "bugs": {"url": "https://github.com/apify/apify-client-js/issues"}, "homepage": "https://docs.apify.com/api/client/js/", "files": ["dist", "!dist/*.tsbuildinfo"], "scripts": {"build": "npm run clean && npm run build:node && npm run build:browser", "postbuild": "gen-esm-wrapper dist/index.js dist/index.mjs", "prepublishOnly": "(test $CI || (echo \"Publishing is reserved to CI!\"; exit 1))", "clean": "<PERSON><PERSON><PERSON> dist", "test": "npm run build && jest", "lint": "eslint", "lint:fix": "eslint --fix", "tsc-check-tests": "tsc --noEmit --project test/tsconfig.json", "format": "prettier --write .", "format:check": "prettier --check .", "build:node": "tsc", "build:browser": "rsbuild build"}, "dependencies": {"@apify/consts": "^2.25.0", "@apify/log": "^2.2.6", "@crawlee/types": "^3.3.0", "agentkeepalive": "^4.2.1", "async-retry": "^1.3.3", "axios": "^1.6.7", "content-type": "^1.0.5", "ow": "^0.28.2", "tslib": "^2.5.0", "type-fest": "^4.0.0"}, "devDependencies": {"@apify/eslint-config": "^1.0.0", "@apify/tsconfig": "^0.1.1", "@babel/cli": "^7.21.0", "@babel/core": "^7.21.0", "@babel/preset-env": "^7.20.2", "@babel/register": "^7.21.0", "@crawlee/puppeteer": "^3.2.2", "@rsbuild/core": "^1.3.6", "@rsbuild/plugin-node-polyfill": "^1.3.0", "@stylistic/eslint-plugin-ts": "^4.2.0", "@types/async-retry": "^1.4.5", "@types/content-type": "^1.1.5", "@types/express": "^4.17.17", "@types/fs-extra": "^11.0.1", "@types/jest": "^29.4.0", "@types/node": "^22.0.0", "ajv": "^8.17.1", "babel-loader": "^10.0.0", "body-parser": "^1.20.3", "compression": "^1.7.4", "eslint": "^9.24.0", "eslint-config-prettier": "^10.1.2", "express": "^4.21.1", "fs-extra": "^11.1.0", "gen-esm-wrapper": "^1.1.2", "globals": "^16.0.0", "jest": "^29.4.3", "prettier": "^3.5.3", "process": "^0.11.10", "puppeteer": "^24.0.0", "rimraf": "^6.0.0", "source-map-support": "^0.5.21", "ts-jest": "^29.0.5", "ts-loader": "^9.4.2", "ts-node": "^10.9.1", "typescript": "^5.8.3", "typescript-eslint": "^8.29.1"}, "packageManager": "npm@10.9.2"}