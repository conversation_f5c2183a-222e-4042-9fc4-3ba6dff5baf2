import type { StorageManagerOptions } from '@crawlee/core';
import { KeyValueStore as CoreKeyValueStore } from '@crawlee/core';
/**
 * @inheritDoc
 */
export declare class KeyValueStore extends CoreKeyValueStore {
    /**
     * Returns a URL for the given key that may be used to publicly
     * access the value in the remote key-value store.
     */
    getPublicUrl(key: string): string;
    /**
     * @inheritDoc
     */
    static open(storeIdOrName?: string | null, options?: StorageManagerOptions): Promise<KeyValueStore>;
}
//# sourceMappingURL=key_value_store.d.ts.map