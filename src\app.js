import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import compression from 'compression';
import morgan from 'morgan';
import rateLimit from 'express-rate-limit';
import cron from 'node-cron';
import dotenv from 'dotenv';

import pricingRoutes from './routes/pricing.routes.js';
import PricingService from './services/pricing.service.js';

// Load environment variables
dotenv.config();

const app = express();
const PORT = process.env.PORT || 3000;
const pricingService = new PricingService();

// Security middleware
app.use(helmet());
app.use(cors({
  origin: process.env.ALLOWED_ORIGINS?.split(',') || ['http://localhost:3000'],
  credentials: true
}));

// Rate limiting
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100, // limit each IP to 100 requests per windowMs
  message: {
    success: false,
    error: 'Too many requests from this IP, please try again later.'
  }
});
app.use('/api/', limiter);

// Body parsing middleware
app.use(compression());
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true }));

// Logging
app.use(morgan('combined'));

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({
    success: true,
    message: 'Outlook Catchall Pricing System is running',
    timestamp: new Date().toISOString(),
    version: process.env.npm_package_version || '1.0.0'
  });
});

// API routes
app.use('/api/pricing', pricingRoutes);

// Root endpoint
app.get('/', (req, res) => {
  res.json({
    success: true,
    message: 'Outlook Catchall Pricing System API',
    version: '1.0.0',
    endpoints: {
      health: '/health',
      pricing: '/api/pricing',
      docs: '/api/docs'
    }
  });
});

// Error handling middleware
app.use((err, req, res, next) => {
  console.error('Error:', err);
  
  res.status(err.status || 500).json({
    success: false,
    error: process.env.NODE_ENV === 'production' 
      ? 'Internal server error' 
      : err.message,
    ...(process.env.NODE_ENV !== 'production' && { stack: err.stack })
  });
});

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({
    success: false,
    error: 'Endpoint not found'
  });
});

// Set up cron job for cleaning up expired subscriptions
// Run daily at 2 AM
cron.schedule('0 2 * * *', async () => {
  console.log('🧹 Running daily cleanup of expired subscriptions...');
  try {
    const cleanedUp = await pricingService.cleanupExpiredSubscriptions();
    console.log(`✅ Cleanup completed: ${cleanedUp} subscriptions processed`);
  } catch (error) {
    console.error('❌ Cleanup failed:', error);
  }
});

// Initialize pricing service on startup
async function initializeApp() {
  try {
    console.log('🚀 Initializing Outlook Catchall Pricing System...');
    
    // Initialize default pricing plan
    await pricingService.initializeDefaultPlan();
    console.log('✅ Default pricing plan initialized');
    
    // Start server
    app.listen(PORT, () => {
      console.log(`🌟 Server running on port ${PORT}`);
      console.log(`📊 Health check: http://localhost:${PORT}/health`);
      console.log(`💰 Pricing API: http://localhost:${PORT}/api/pricing`);
    });
  } catch (error) {
    console.error('❌ Failed to initialize app:', error);
    process.exit(1);
  }
}

// Handle graceful shutdown
process.on('SIGTERM', () => {
  console.log('🛑 SIGTERM received, shutting down gracefully...');
  process.exit(0);
});

process.on('SIGINT', () => {
  console.log('🛑 SIGINT received, shutting down gracefully...');
  process.exit(0);
});

// Start the application
initializeApp();

export default app;
