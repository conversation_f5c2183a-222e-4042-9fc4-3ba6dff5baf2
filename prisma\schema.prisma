// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql" // or "mysql", "sqlite", etc.
  url      = env("DATABASE_URL")
}

model User {
  id        String   @id @default(cuid())
  email     String   @unique
  name      String?
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Subscription relationship
  subscription Subscription?
  
  // Credit transactions
  creditTransactions CreditTransaction[]
  
  // Usage records
  usageRecords UsageRecord[]

  @@map("users")
}

model Subscription {
  id        String   @id @default(cuid())
  userId    String   @unique
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  
  planId    String
  plan      SubscriptionPlan @relation(fields: [planId], references: [id])
  
  status    SubscriptionStatus @default(ACTIVE)
  
  // Current billing period
  currentPeriodStart DateTime
  currentPeriodEnd   DateTime
  
  // Credits for current period
  creditsRemaining   Int      @default(0)
  creditsTotal       Int      @default(0)
  
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("subscriptions")
}

model SubscriptionPlan {
  id          String @id @default(cuid())
  name        String
  description String?
  
  // Pricing details
  pricePerUnit    Decimal @db.Decimal(10, 2) // $20.00
  creditsPerUnit  Int     // 10000 credits
  
  // Billing cycle
  billingCycle    BillingCycle @default(MONTHLY)
  
  // Credit expiration
  creditExpiryDays Int @default(30) // Credits expire in 30 days
  
  isActive    Boolean  @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relationships
  subscriptions Subscription[]

  @@map("subscription_plans")
}

model CreditTransaction {
  id          String    @id @default(cuid())
  userId      String
  user        User      @relation(fields: [userId], references: [id], onDelete: Cascade)
  
  type        CreditTransactionType
  amount      Int       // Positive for credits added, negative for credits used
  description String?
  
  // For tracking credit expiration
  expiresAt   DateTime?
  
  // Reference to subscription if applicable
  subscriptionId String?
  
  createdAt   DateTime  @default(now())

  @@map("credit_transactions")
}

model UsageRecord {
  id          String   @id @default(cuid())
  userId      String
  user        User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  
  // Usage details
  service     String   // e.g., "email_verification", "outlook_catchall"
  quantity    Int      // Number of emails processed
  creditsUsed Int      // Credits deducted
  
  // Metadata
  metadata    Json?    // Additional data about the usage
  
  createdAt   DateTime @default(now())

  @@map("usage_records")
}

// Enums
enum SubscriptionStatus {
  ACTIVE
  INACTIVE
  CANCELLED
  EXPIRED
  SUSPENDED
}

enum BillingCycle {
  MONTHLY
  YEARLY
}

enum CreditTransactionType {
  PURCHASE
  USAGE
  REFUND
  EXPIRATION
  BONUS
}
