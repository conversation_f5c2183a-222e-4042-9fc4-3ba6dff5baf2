import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

export class PricingService {
  constructor() {
    this.defaultPlan = {
      name: 'Standard Plan',
      description: 'Email verification and processing',
      pricePerUnit: 20.00, // $20 per 10K credits
      creditsPerUnit: 10000,
      billingCycle: 'MONTHLY',
      creditExpiryDays: 30
    };
  }

  /**
   * Initialize default pricing plan
   */
  async initializeDefaultPlan() {
    try {
      const existingPlan = await prisma.subscriptionPlan.findFirst({
        where: { name: this.defaultPlan.name }
      });

      if (!existingPlan) {
        return await prisma.subscriptionPlan.create({
          data: this.defaultPlan
        });
      }

      return existingPlan;
    } catch (error) {
      console.error('Error initializing default plan:', error);
      throw error;
    }
  }

  /**
   * Create or update user subscription
   */
  async createSubscription(userId, planId, creditsAmount = 10000) {
    try {
      const plan = await prisma.subscriptionPlan.findUnique({
        where: { id: planId }
      });

      if (!plan) {
        throw new Error('Subscription plan not found');
      }

      const now = new Date();
      const periodEnd = new Date(now);
      periodEnd.setDate(periodEnd.getDate() + plan.creditExpiryDays);

      // Upsert subscription
      const subscription = await prisma.subscription.upsert({
        where: { userId },
        update: {
          planId,
          currentPeriodStart: now,
          currentPeriodEnd: periodEnd,
          creditsRemaining: creditsAmount,
          creditsTotal: creditsAmount,
          status: 'ACTIVE'
        },
        create: {
          userId,
          planId,
          currentPeriodStart: now,
          currentPeriodEnd: periodEnd,
          creditsRemaining: creditsAmount,
          creditsTotal: creditsAmount,
          status: 'ACTIVE'
        }
      });

      // Record credit transaction
      await prisma.creditTransaction.create({
        data: {
          userId,
          type: 'PURCHASE',
          amount: creditsAmount,
          description: `Credits purchased - ${plan.name}`,
          expiresAt: periodEnd,
          subscriptionId: subscription.id
        }
      });

      return subscription;
    } catch (error) {
      console.error('Error creating subscription:', error);
      throw error;
    }
  }

  /**
   * Use credits for a service
   */
  async useCredits(userId, service, quantity, creditsPerItem = 1) {
    try {
      const subscription = await prisma.subscription.findUnique({
        where: { userId },
        include: { plan: true }
      });

      if (!subscription) {
        throw new Error('No active subscription found');
      }

      if (subscription.status !== 'ACTIVE') {
        throw new Error('Subscription is not active');
      }

      // Check if subscription has expired
      if (new Date() > subscription.currentPeriodEnd) {
        await this.expireSubscription(userId);
        throw new Error('Subscription has expired');
      }

      const creditsNeeded = quantity * creditsPerItem;

      if (subscription.creditsRemaining < creditsNeeded) {
        throw new Error(`Insufficient credits. Need ${creditsNeeded}, have ${subscription.creditsRemaining}`);
      }

      // Deduct credits
      const updatedSubscription = await prisma.subscription.update({
        where: { userId },
        data: {
          creditsRemaining: subscription.creditsRemaining - creditsNeeded
        }
      });

      // Record usage
      await prisma.usageRecord.create({
        data: {
          userId,
          service,
          quantity,
          creditsUsed: creditsNeeded,
          metadata: {
            creditsPerItem,
            remainingCredits: updatedSubscription.creditsRemaining
          }
        }
      });

      // Record credit transaction
      await prisma.creditTransaction.create({
        data: {
          userId,
          type: 'USAGE',
          amount: -creditsNeeded,
          description: `Credits used for ${service} (${quantity} items)`,
          subscriptionId: subscription.id
        }
      });

      return {
        success: true,
        creditsUsed: creditsNeeded,
        creditsRemaining: updatedSubscription.creditsRemaining,
        quantity
      };
    } catch (error) {
      console.error('Error using credits:', error);
      throw error;
    }
  }

  /**
   * Get user's current credit balance and subscription status
   */
  async getUserCredits(userId) {
    try {
      const subscription = await prisma.subscription.findUnique({
        where: { userId },
        include: { 
          plan: true,
          user: true
        }
      });

      if (!subscription) {
        return {
          hasSubscription: false,
          creditsRemaining: 0,
          status: 'NO_SUBSCRIPTION'
        };
      }

      // Check if expired
      const now = new Date();
      if (now > subscription.currentPeriodEnd) {
        await this.expireSubscription(userId);
        return {
          hasSubscription: true,
          creditsRemaining: 0,
          status: 'EXPIRED',
          expiredAt: subscription.currentPeriodEnd
        };
      }

      return {
        hasSubscription: true,
        creditsRemaining: subscription.creditsRemaining,
        creditsTotal: subscription.creditsTotal,
        status: subscription.status,
        currentPeriodEnd: subscription.currentPeriodEnd,
        plan: subscription.plan
      };
    } catch (error) {
      console.error('Error getting user credits:', error);
      throw error;
    }
  }

  /**
   * Expire subscription and handle credit expiration
   */
  async expireSubscription(userId) {
    try {
      const subscription = await prisma.subscription.findUnique({
        where: { userId }
      });

      if (!subscription) return;

      // Update subscription status
      await prisma.subscription.update({
        where: { userId },
        data: {
          status: 'EXPIRED',
          creditsRemaining: 0
        }
      });

      // Record credit expiration if there were remaining credits
      if (subscription.creditsRemaining > 0) {
        await prisma.creditTransaction.create({
          data: {
            userId,
            type: 'EXPIRATION',
            amount: -subscription.creditsRemaining,
            description: 'Credits expired due to subscription period end',
            subscriptionId: subscription.id
          }
        });
      }

      return true;
    } catch (error) {
      console.error('Error expiring subscription:', error);
      throw error;
    }
  }

  /**
   * Get pricing information
   */
  async getPricingPlans() {
    try {
      return await prisma.subscriptionPlan.findMany({
        where: { isActive: true },
        orderBy: { pricePerUnit: 'asc' }
      });
    } catch (error) {
      console.error('Error getting pricing plans:', error);
      throw error;
    }
  }

  /**
   * Clean up expired subscriptions (run as cron job)
   */
  async cleanupExpiredSubscriptions() {
    try {
      const expiredSubscriptions = await prisma.subscription.findMany({
        where: {
          currentPeriodEnd: {
            lt: new Date()
          },
          status: 'ACTIVE'
        }
      });

      for (const subscription of expiredSubscriptions) {
        await this.expireSubscription(subscription.userId);
      }

      console.log(`Cleaned up ${expiredSubscriptions.length} expired subscriptions`);
      return expiredSubscriptions.length;
    } catch (error) {
      console.error('Error cleaning up expired subscriptions:', error);
      throw error;
    }
  }
}

  /**
   * Add bonus credits to user
   */
  async addBonusCredits(userId, amount, description = 'Bonus credits') {
    try {
      const subscription = await prisma.subscription.findUnique({
        where: { userId }
      });

      if (!subscription) {
        throw new Error('No subscription found for user');
      }

      // Add credits to subscription
      const updatedSubscription = await prisma.subscription.update({
        where: { userId },
        data: {
          creditsRemaining: subscription.creditsRemaining + amount,
          creditsTotal: subscription.creditsTotal + amount
        }
      });

      // Record transaction
      await prisma.creditTransaction.create({
        data: {
          userId,
          type: 'BONUS',
          amount,
          description,
          expiresAt: subscription.currentPeriodEnd,
          subscriptionId: subscription.id
        }
      });

      return updatedSubscription;
    } catch (error) {
      console.error('Error adding bonus credits:', error);
      throw error;
    }
  }
}

export default PricingService;
