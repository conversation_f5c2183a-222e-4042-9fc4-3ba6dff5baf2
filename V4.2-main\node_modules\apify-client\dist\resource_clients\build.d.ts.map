{"version": 3, "file": "build.d.ts", "sourceRoot": "", "sources": ["../../src/resource_clients/build.ts"], "names": [], "mappings": "AAEA,OAAO,KAAK,EAAE,yBAAyB,EAAE,MAAM,eAAe,CAAC;AAE/D,OAAO,KAAK,EAAE,2BAA2B,EAAE,MAAM,oBAAoB,CAAC;AACtE,OAAO,EAAE,cAAc,EAAE,MAAM,yBAAyB,CAAC;AAEzD,OAAO,KAAK,EAAE,eAAe,EAAE,MAAM,SAAS,CAAC;AAC/C,OAAO,EAAE,SAAS,EAAE,MAAM,OAAO,CAAC;AAElC,qBAAa,WAAY,SAAQ,cAAc;IAC3C;;OAEG;gBACS,OAAO,EAAE,2BAA2B;IAOhD;;OAEG;IACG,GAAG,CAAC,OAAO,GAAE,qBAA0B,GAAG,OAAO,CAAC,KAAK,GAAG,SAAS,CAAC;IAW1E;;OAEG;IACG,KAAK,IAAI,OAAO,CAAC,KAAK,CAAC;IAU7B;;OAEG;IACG,MAAM,IAAI,OAAO,CAAC,IAAI,CAAC;IAI7B;;OAEG;IACG,oBAAoB,IAAI,OAAO,CAAC,iBAAiB,CAAC;IAUxD;;;;;;;;;;OAUG;IACG,aAAa,CAAC,OAAO,GAAE,+BAAoC,GAAG,OAAO,CAAC,KAAK,CAAC;IAWlF;;OAEG;IACH,GAAG,IAAI,SAAS;CAOnB;AAED,MAAM,WAAW,qBAAqB;IAClC,aAAa,CAAC,EAAE,MAAM,CAAC;CAC1B;AAED,MAAM,WAAW,+BAA+B;IAC5C;;;;OAIG;IACH,QAAQ,CAAC,EAAE,MAAM,CAAC;CACrB;AAED,MAAM,WAAW,SAAS;IACtB,MAAM,EAAE,MAAM,CAAC;IACf,QAAQ,EAAE,MAAM,CAAC;IACjB,SAAS,EAAE,MAAM,CAAC;CACrB;AAED,MAAM,WAAW,KAAK;IAClB,EAAE,EAAE,MAAM,CAAC;IACX,KAAK,EAAE,MAAM,CAAC;IACd,MAAM,EAAE,MAAM,CAAC;IACf,SAAS,EAAE,IAAI,CAAC;IAChB,UAAU,CAAC,EAAE,IAAI,CAAC;IAClB,MAAM,EAAE,CAAC,OAAO,yBAAyB,CAAC,CAAC,MAAM,CAAC,CAAC;IACnD,IAAI,EAAE,SAAS,CAAC;IAChB,KAAK,CAAC,EAAE,UAAU,CAAC;IACnB,OAAO,CAAC,EAAE,YAAY,CAAC;IACvB;;OAEG;IACH,WAAW,CAAC,EAAE,MAAM,CAAC;IACrB;;OAEG;IACH,MAAM,CAAC,EAAE,MAAM,CAAC;IAChB,WAAW,EAAE,MAAM,CAAC;IACpB,KAAK,CAAC,EAAE,UAAU,CAAC;IACnB,aAAa,CAAC,EAAE,MAAM,CAAC;IACvB,QAAQ,CAAC,EAAE,UAAU,CAAC;IACtB,eAAe,CAAC,EAAE,eAAe,CAAC;CACrC;AAED,MAAM,WAAW,UAAU;IACvB,mBAAmB,CAAC,EAAE,MAAM,CAAC;CAChC;AAED,MAAM,WAAW,UAAU;IACvB,cAAc,EAAE,MAAM,CAAC;IACvB,WAAW,EAAE,MAAM,CAAC;IACpB,YAAY,EAAE,MAAM,CAAC;CACxB;AAED,MAAM,WAAW,YAAY;IACzB,QAAQ,CAAC,EAAE,OAAO,CAAC;IACnB,YAAY,CAAC,EAAE,OAAO,CAAC;IACvB,YAAY,CAAC,EAAE,MAAM,CAAC;IACtB,UAAU,CAAC,EAAE,MAAM,CAAC;CACvB;AAED,MAAM,WAAW,iBAAiB;IAC9B,OAAO,EAAE,MAAM,CAAC;IAChB,IAAI,EAAE;QACF,KAAK,EAAE,MAAM,CAAC;QACd,WAAW,CAAC,EAAE,MAAM,CAAC;QACrB,OAAO,CAAC,EAAE,MAAM,CAAC;QACjB,YAAY,EAAE,MAAM,CAAC;KACxB,CAAC;IACF,OAAO,EAAE;QAAE,GAAG,EAAE,MAAM,CAAA;KAAE,EAAE,CAAC;IAC3B,KAAK,EAAE;QAAE,CAAC,GAAG,EAAE,MAAM,GAAG;YAAE,IAAI,EAAE,gBAAgB,CAAA;SAAE,CAAA;KAAE,CAAC;IACrD,UAAU,EAAE;QACR,OAAO,EAAE;YACL,CAAC,GAAG,EAAE,MAAM,GAAG,MAAM,CAAC;SACzB,CAAC;KACL,CAAC;CACL;AAED,UAAU,gBAAgB;IACtB,WAAW,EAAE,MAAM,CAAC;IACpB,0BAA0B,EAAE,OAAO,CAAC;IACpC,OAAO,EAAE,MAAM,CAAC;IAChB,IAAI,EAAE,MAAM,EAAE,CAAC;IACf,WAAW,EAAE;QACT,QAAQ,EAAE,OAAO,CAAC;QAClB,OAAO,EAAE;YACL,kBAAkB,EAAE;gBAChB,MAAM,EAAE;oBACJ,IAAI,EAAE,MAAM,CAAC;iBAChB,CAAC;aACL,CAAC;SACL,CAAC;KACL,CAAC;IACF,UAAU,EAAE;QACR,IAAI,EAAE,MAAM,CAAC;QACb,EAAE,EAAE,MAAM,CAAC;QACX,QAAQ,EAAE,OAAO,CAAC;QAClB,MAAM,EAAE;YACJ,IAAI,EAAE,MAAM,CAAC;SAChB,CAAC;QACF,WAAW,EAAE,MAAM,CAAC;KACvB,EAAE,CAAC;IACJ,SAAS,EAAE;QACP,KAAK,EAAE;YACH,WAAW,EAAE,MAAM,CAAC;YACpB,OAAO,CAAC,EAAE;gBACN,kBAAkB,EAAE;oBAChB,MAAM,EAAE;wBACJ,IAAI,EAAE,MAAM,CAAC;qBAChB,CAAC;iBACL,CAAC;aACL,CAAC;SACL,CAAC;KACL,CAAC;CACL"}