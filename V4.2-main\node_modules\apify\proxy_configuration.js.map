{"version": 3, "file": "proxy_configuration.js", "sourceRoot": "", "sources": ["../src/proxy_configuration.ts"], "names": [], "mappings": ";;;;AAIA,wCAA6E;AAC7E,0CAA6C;AAC7C,oDAAoB;AAEpB,0CAAwE;AACxE,gDAAwD;AAExD,yCAAmC;AACnC,yDAAmD;AAEnD,oEAAoE;AACpE,MAAM,qBAAqB,GAAG,EAAE,CAAC;AACjC,MAAM,mCAAmC,GAAG,IAAK,CAAC;AAClD,MAAM,yBAAyB,GAAG,CAAC,CAAC;AACpC,MAAM,kBAAkB,GAAG,YAAY,CAAC;AA8GxC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAgCG;AACH,MAAa,kBAAmB,SAAQ,yBAAsB;IAQ1D;;OAEG;IACH,YACI,UAAqC,EAAE,EAC9B,SAAS,gCAAa,CAAC,eAAe,EAAE;QAEjD,MAAM,EAAE,SAAS,EAAE,cAAc,EAAE,GAAG,IAAI,EAAE,GAAG,OAAO,CAAC;QACvD,KAAK,CAAC;YACF,SAAS;YACT,cAAc;YACd,CAAC,kBAA4B,CAAC,EAAE,KAAK;SACxC,CAAC,CAAC;QAPH;;;;mBAAS,MAAM;WAAkC;QAZ7C;;;;;WAAiB;QACjB;;;;;WAAqB;QACrB;;;;;WAAkB;QAClB;;;;;WAAiB;QACjB;;;;;WAAc;QACd;;;;;WAAyB;QAe7B,IAAA,YAAE,EACE,IAAI,EACJ,YAAE,CAAC,MAAM,CAAC,UAAU,CAAC;YACjB,MAAM,EAAE,YAAE,CAAC,QAAQ,CAAC,KAAK,CAAC,MAAM,CAC5B,YAAE,CAAC,MAAM,CAAC,OAAO,CAAC,gCAAuB,CAAC,CAC7C;YACD,gBAAgB,EAAE,YAAE,CAAC,QAAQ,CAAC,KAAK,CAAC,MAAM,CACtC,YAAE,CAAC,MAAM,CAAC,OAAO,CAAC,gCAAuB,CAAC,CAC7C;YACD,WAAW,EAAE,YAAE,CAAC,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,kBAAkB,CAAC;YAC3D,iBAAiB,EACb,YAAE,CAAC,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,kBAAkB,CAAC;YAClD,QAAQ,EAAE,YAAE,CAAC,QAAQ,CAAC,MAAM;YAC5B,eAAe,EAAE,YAAE,CAAC,QAAQ,CAAC,KAAK,CAAC,MAAM,CACrC,YAAE,CAAC,KAAK,CAAC,MAAM,CAAC,YAAE,CAAC,MAAM,CAAC,CAC7B;YACD,iBAAiB,EAAE,YAAE,CAAC,QAAQ,CAAC,KAAK,CAAC,MAAM,CAAC,YAAE,CAAC,MAAM,CAAC;SACzD,CAAC,CACL,CAAC;QAEF,MAAM,EACF,MAAM,GAAG,EAAE,EACX,gBAAgB,GAAG,EAAE,EACrB,WAAW,EACX,iBAAiB,EACjB,QAAQ,GAAG,MAAM,CAAC,GAAG,CAAC,eAAe,CAAC,EACtC,iBAAiB,EACjB,eAAe,GAClB,GAAG,OAAO,CAAC;QAEZ,IAAI,CAAC,eAAe,KAApB,IAAI,CAAC,eAAe,GAAK,eAAe,EAAC;QAEzC,IAAI,iBAAiB,EAAE,CAAC;YACpB,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,wBAAwB,CAChD,iBAAiB,EACjB,OAAO,CACV,CAAC;QACN,CAAC;QAED,MAAM,WAAW,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,gBAAgB,CAAC;QAC9D,MAAM,gBAAgB,GAAG,WAAW,IAAI,iBAAiB,CAAC;QAC1D,MAAM,QAAQ,GAAG,MAAM,CAAC,GAAG,CAAC,eAAe,CAAC,CAAC;QAC7C,MAAM,IAAI,GAAG,MAAM,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;QAErC,aAAa;QACb,IACI,CAAC,SAAS,IAAI,cAAc,CAAC;YAC7B,CAAC,WAAW,CAAC,MAAM,IAAI,gBAAgB,CAAC,EAC1C,CAAC;YACC,IAAI,CAAC,kCAAkC,EAAE,CAAC;QAC9C,CAAC;QACD,IAAI,SAAS,IAAI,cAAc;YAC3B,IAAI,CAAC,gCAAgC,EAAE,CAAC;QAE5C,IAAI,CAAC,MAAM,GAAG,WAAW,CAAC;QAC1B,IAAI,CAAC,WAAW,GAAG,gBAAgB,CAAC;QACpC,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;QACzB,IAAI,CAAC,QAAQ,GAAG,QAAS,CAAC;QAC1B,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;QACjB,IAAI,CAAC,cAAc,GAAG,CAAC,IAAI,CAAC,SAAS,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC;QAE9D,IAAI,SAAS,IAAI,SAAS,CAAC,IAAI,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC,EAAE,CAAC;YAClE,IAAI,CAAC,GAAG,CAAC,OAAO,CACZ,uHAAuH;gBACnH,kFAAkF,CACzF,CAAC;QACN,CAAC;IACL,CAAC;IAED;;;;;;;OAOG;IACH,KAAK,CAAC,UAAU;QACZ,IAAI,IAAI,CAAC,cAAc,EAAE,CAAC;YACtB,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC;gBACjB,MAAM,IAAI,CAAC,mBAAmB,EAAE,CAAC;YACrC,CAAC;YAED,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC;gBACjB,IAAI,gBAAK,CAAC,QAAQ,EAAE,EAAE,CAAC;oBACnB,MAAM,IAAI,KAAK,CACX,wEAAwE,uBAAc,CAAC,cAAc,0BAA0B;wBAC3H,kDAAkD,uBAAc,CAAC,KAAK,0BAA0B;wBAChG,qEAAqE,uBAAc,CAAC,cAAc,iBAAiB,CAC1H,CAAC;gBACN,CAAC;qBAAM,CAAC;oBACJ,IAAI,CAAC,GAAG,CAAC,OAAO,CACZ,0FAA0F;wBACtF,gCAAgC,uBAAc,CAAC,cAAc,0BAA0B;wBACvF,kDAAkD,uBAAc,CAAC,KAAK,0BAA0B;wBAChG,qEAAqE,uBAAc,CAAC,cAAc,iBAAiB,CAC1H,CAAC;gBACN,CAAC;YACL,CAAC;YAED,OAAO,IAAI,CAAC,YAAY,EAAE,CAAC;QAC/B,CAAC;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;;;;;;;;;;;;;OAgBG;IACM,KAAK,CAAC,YAAY,CACvB,SAA2B,EAC3B,OAA+D;QAE/D,IAAI,OAAO,SAAS,KAAK,QAAQ;YAAE,SAAS,GAAG,GAAG,SAAS,EAAE,CAAC;QAC9D,IAAA,YAAE,EACE,SAAS,EACT,YAAE,CAAC,QAAQ,CAAC,MAAM;aACb,SAAS,CAAC,qBAAqB,CAAC;aAChC,OAAO,CAAC,gCAAuB,CAAC,CACxC,CAAC;QAEF,MAAM,SAAS,GAAG,MAAM,KAAK,CAAC,YAAY,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;QAC/D,IAAI,CAAC,SAAS;YAAE,OAAO,SAAS,CAAC;QAEjC,MAAM,EAAE,MAAM,EAAE,WAAW,EAAE,QAAQ,EAAE,IAAI,EAAE,QAAQ,EAAE,GAAG,CACtD,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,GAAG,CAAC,SAAS,CAAC,GAAG,CAAC,CAChC,CAAC;QAExB,OAAO;YACH,GAAG,SAAS;YACZ,SAAS;YACT,MAAM;YACN,WAAW;YACX,4FAA4F;YAC5F,QAAQ,EAAE,IAAI,CAAC,cAAc;gBACzB,CAAC,CAAC,CAAC,QAAQ,IAAI,EAAE,CAAC;gBAClB,CAAC,CAAC,kBAAkB,CAAC,QAAS,CAAC;YACnC,QAAQ;YACR,IAAI,EAAE,IAAK;SACd,CAAC;IACN,CAAC;IAED;;;;;;;;;;;;OAYG;IACM,KAAK,CAAC,MAAM,CACjB,SAA2B,EAC3B,OAAyD;QAEzD,IAAI,OAAO,SAAS,KAAK,QAAQ;YAAE,SAAS,GAAG,GAAG,SAAS,EAAE,CAAC;QAC9D,IAAA,YAAE,EACE,SAAS,EACT,YAAE,CAAC,QAAQ,CAAC,MAAM;aACb,SAAS,CAAC,qBAAqB,CAAC;aAChC,OAAO,CAAC,gCAAuB,CAAC,CACxC,CAAC;QACF,IAAI,IAAI,CAAC,cAAc,EAAE,CAAC;YACtB,OAAO,CACH,CAAC,MAAM,IAAI,CAAC,mBAAmB,CAAC,SAAS,EAAE;gBACvC,OAAO,EAAE,OAAO,EAAE,OAAO;aAC5B,CAAC,CAAC,IAAI,SAAS,CACnB,CAAC;QACN,CAAC;QACD,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;YACjB,OAAO,IAAI,CAAC,gBAAgB,CAAC,SAAS,CAAC,CAAC;QAC5C,CAAC;QAED,IAAI,IAAI,CAAC,eAAe,EAAE,CAAC;YACvB,OAAO,CACH,IAAI,CAAC,gBAAgB,CACjB,SAAS,IAAI,IAAA,gCAAoB,EAAC,CAAC,CAAC,EACpC,OAAO,CACV,CAAC,QAAQ,IAAI,SAAS,CAC1B,CAAC;QACN,CAAC;QAED,OAAO,IAAI,CAAC,iBAAiB,CAAC,SAAS,CAAC,CAAC;IAC7C,CAAC;IAES,wBAAwB,CAC9B,iBAEC,EACD,aAAwC;QAExC,OAAO,iBAAiB,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC;YACrC,IAAI,kBAAkB,CAAC;gBACnB,GAAG,aAAa;gBAChB,GAAG,MAAM;gBACT,iBAAiB,EAAE,SAAS;aAC/B,CAAC,CAAC,iBAAiB,EAAE;SACzB,CAAC,CAAC;IACP,CAAC;IAED;;OAEG;IACO,YAAY,CAAC,SAAkB;QACrC,IAAI,QAAQ,CAAC;QACb,MAAM,EAAE,MAAM,EAAE,WAAW,EAAE,GAAG,IAAI,CAAC;QACrC,MAAM,KAAK,GAAa,EAAE,CAAC;QAE3B,IAAI,MAAM,IAAI,MAAM,CAAC,MAAM,EAAE,CAAC;YAC1B,KAAK,CAAC,IAAI,CAAC,UAAU,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;QAC7C,CAAC;QACD,IAAI,SAAS,EAAE,CAAC;YACZ,KAAK,CAAC,IAAI,CAAC,WAAW,SAAS,EAAE,CAAC,CAAC;QACvC,CAAC;QACD,IAAI,WAAW,EAAE,CAAC;YACd,KAAK,CAAC,IAAI,CAAC,WAAW,WAAW,EAAE,CAAC,CAAC;QACzC,CAAC;QAED,QAAQ,GAAG,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QAE3B,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC;YAAE,QAAQ,GAAG,MAAM,CAAC;QAE1C,OAAO,QAAQ,CAAC;IACpB,CAAC;IAES,iBAAiB,CAAC,SAAkB;QAC1C,MAAM,QAAQ,GAAG,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC;QAC9C,MAAM,GAAG,GAAG,IAAI,GAAG,CAAC,UAAU,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC;QAC5D,GAAG,CAAC,QAAQ,GAAG,GAAG,QAAQ,EAAE,CAAC;QAC7B,GAAG,CAAC,QAAQ,GAAG,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;QAClC,MAAM,SAAS,GAAG,GAAG,CAAC,QAAQ,EAAE,CAAC;QAEjC,OAAO,SAAS,CAAC,SAAS,CAAC,CAAC,EAAE,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;IACxD,CAAC;IAED;;OAEG;IACH,0BAA0B;IAChB,KAAK,CAAC,mBAAmB;QAC/B,MAAM,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;QAEvC,IAAI,CAAC,KAAK;YAAE,OAAO;QACnB,IAAI,CAAC;YACD,MAAM,IAAI,GAAG,MAAM,gBAAK,CAAC,WAAW,CAAC,IAAI,EAAE,CAAC,GAAG,EAAE,CAAC;YAClD,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,KAAK,EAAE,QAAQ,CAAC;QACzC,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,IAAI,gBAAK,CAAC,QAAQ,EAAE,EAAE,CAAC;gBACnB,MAAM,KAAK,CAAC;YAChB,CAAC;iBAAM,CAAC;gBACJ,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,uCAAuC,EAAE;oBACtD,KAAK;iBACR,CAAC,CAAC;YACP,CAAC;QACL,CAAC;IACL,CAAC;IAED;;;;OAIG;IACO,KAAK,CAAC,YAAY;QACxB,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,YAAY,EAAE,CAAC;QAEzC,IAAI,CAAC,MAAM,EAAE,CAAC;YACV,IAAI,CAAC,GAAG,CAAC,OAAO,CACZ,iFAAiF;gBAC7E,wHAAwH,CAC/H,CAAC;YACF,OAAO,IAAI,CAAC;QAChB,CAAC;QAED,MAAM,EAAE,SAAS,EAAE,eAAe,EAAE,gBAAgB,EAAE,GAAG,MAAM,CAAC;QAChE,IAAI,CAAC,gBAAgB,GAAG,gBAAgB,CAAC;QAEzC,IAAI,SAAS,EAAE,CAAC;YACZ,OAAO,IAAI,CAAC;QAChB,CAAC;QAED,kGAAkG;QAClG,uEAAuE;QACvE,8EAA8E;QAC9E,IAAI,gBAAK,CAAC,QAAQ,EAAE,EAAE,CAAC;YACnB,MAAM,IAAI,KAAK,CAAC,eAAe,CAAC,CAAC;QACrC,CAAC;QAED,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,eAAe,CAAC,CAAC;QAClC,OAAO,KAAK,CAAC;IACjB,CAAC;IAED;;OAEG;IACO,KAAK,CAAC,YAAY;QAQxB,MAAM,cAAc,GAAG,IAAI,CAAC,MAAM,CAAC,GAAG,CAClC,gBAAgB,EAChB,wBAAwB,CAC3B,CAAC;QACF,MAAM,WAAW,GAAG;YAChB,GAAG,EAAE,GAAG,cAAc,eAAe;YACrC,QAAQ,EAAE,MAAM,IAAI,CAAC,MAAM,EAAE;YAC7B,OAAO,EAAE,EAAE,OAAO,EAAE,mCAAmC,EAAE;YACzD,YAAY,EAAE,MAAM;SACd,CAAC;QAEX,KAAK,IAAI,OAAO,GAAG,CAAC,EAAE,OAAO,IAAI,yBAAyB,EAAE,OAAO,EAAE,EAAE,CAAC;YACpE,IAAI,CAAC;gBACD,MAAM,QAAQ,GAAG,MAAM,IAAA,mBAAW,EAI/B,WAAW,CAAC,CAAC;gBAChB,OAAO,QAAQ,CAAC,IAAI,CAAC;YACzB,CAAC;YAAC,MAAM,CAAC;gBACL,0BAA0B;YAC9B,CAAC;QACL,CAAC;QAED,OAAO,SAAS,CAAC;IACrB,CAAC;IAED;;;OAGG;IACO,kCAAkC;QACxC,MAAM,IAAI,KAAK,CACX,kDAAkD;YAC9C,yFAAyF;YACzF,0GAA0G,CACjH,CAAC;IACN,CAAC;CACJ;AA3XD,gDA2XC"}