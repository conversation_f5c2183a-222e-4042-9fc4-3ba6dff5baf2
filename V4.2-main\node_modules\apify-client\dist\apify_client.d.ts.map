{"version": 3, "file": "apify_client.d.ts", "sourceRoot": "", "sources": ["../src/apify_client.ts"], "names": [], "mappings": "AAAA,OAAO,KAAK,EAAE,uBAAuB,EAAE,MAAM,gBAAgB,CAAC;AAI9D,OAAO,KAAK,EAAE,GAAG,EAAE,MAAM,YAAY,CAAC;AAGtC,OAAO,EAAE,UAAU,EAAE,MAAM,eAAe,CAAC;AAC3C,OAAO,KAAK,EAAE,0BAA0B,EAAE,MAAM,gBAAgB,CAAC;AACjE,OAAO,EAAE,WAAW,EAAE,MAAM,0BAA0B,CAAC;AACvD,OAAO,EAAE,qBAAqB,EAAE,MAAM,qCAAqC,CAAC;AAC5E,OAAO,EAAE,WAAW,EAAE,MAAM,0BAA0B,CAAC;AACvD,OAAO,EAAE,qBAAqB,EAAE,MAAM,qCAAqC,CAAC;AAC5E,OAAO,EAAE,aAAa,EAAE,MAAM,4BAA4B,CAAC;AAC3D,OAAO,EAAE,uBAAuB,EAAE,MAAM,uCAAuC,CAAC;AAChF,OAAO,EAAE,mBAAmB,EAAE,MAAM,oCAAoC,CAAC;AACzE,OAAO,EAAE,6BAA6B,EAAE,MAAM,+CAA+C,CAAC;AAC9F,OAAO,EAAE,SAAS,EAAE,MAAM,wBAAwB,CAAC;AACnD,OAAO,KAAK,EAAE,uBAAuB,EAAE,MAAM,kCAAkC,CAAC;AAChF,OAAO,EAAE,kBAAkB,EAAE,MAAM,kCAAkC,CAAC;AACtE,OAAO,EAAE,4BAA4B,EAAE,MAAM,6CAA6C,CAAC;AAC3F,OAAO,EAAE,SAAS,EAAE,MAAM,wBAAwB,CAAC;AACnD,OAAO,EAAE,mBAAmB,EAAE,MAAM,mCAAmC,CAAC;AACxE,OAAO,EAAE,cAAc,EAAE,MAAM,6BAA6B,CAAC;AAC7D,OAAO,EAAE,wBAAwB,EAAE,MAAM,wCAAwC,CAAC;AAClF,OAAO,EAAE,qBAAqB,EAAE,MAAM,qCAAqC,CAAC;AAC5E,OAAO,EAAE,UAAU,EAAE,MAAM,yBAAyB,CAAC;AACrD,OAAO,EAAE,oBAAoB,EAAE,MAAM,oCAAoC,CAAC;AAC1E,OAAO,EAAE,UAAU,EAAE,MAAM,yBAAyB,CAAC;AACrD,OAAO,EAAE,aAAa,EAAE,MAAM,4BAA4B,CAAC;AAC3D,OAAO,EAAE,uBAAuB,EAAE,MAAM,uCAAuC,CAAC;AAChF,OAAO,EAAE,qBAAqB,EAAE,MAAM,qCAAqC,CAAC;AAC5E,OAAO,EAAE,+BAA+B,EAAE,MAAM,gDAAgD,CAAC;AACjG,OAAO,EAAE,UAAU,EAAE,MAAM,cAAc,CAAC;AAI1C;;;GAGG;AACH,qBAAa,WAAW;IACpB,OAAO,EAAE,MAAM,CAAC;IAEhB,KAAK,CAAC,EAAE,MAAM,CAAC;IAEf,KAAK,EAAE,UAAU,CAAC;IAElB,MAAM,EAAE,GAAG,CAAC;IAEZ,UAAU,EAAE,UAAU,CAAC;gBAEX,OAAO,GAAE,kBAAuB;IAwC5C,OAAO,CAAC,QAAQ;IAQhB;;OAEG;IACH,MAAM,IAAI,qBAAqB;IAI/B;;OAEG;IACH,KAAK,CAAC,EAAE,EAAE,MAAM,GAAG,WAAW;IAS9B;;OAEG;IACH,MAAM,IAAI,qBAAqB;IAI/B;;OAEG;IACH,KAAK,CAAC,EAAE,EAAE,MAAM,GAAG,WAAW;IAS9B;;OAEG;IACH,QAAQ,IAAI,uBAAuB;IAInC;;OAEG;IACH,OAAO,CAAC,IAAI,SAAS,MAAM,CAAC,MAAM,GAAG,MAAM,EAAE,GAAG,CAAC,GAAG,MAAM,CAAC,MAAM,GAAG,MAAM,EAAE,OAAO,CAAC,EAChF,EAAE,EAAE,MAAM,GACX,aAAa,CAAC,IAAI,CAAC;IAStB;;OAEG;IACH,cAAc,IAAI,6BAA6B;IAI/C;;OAEG;IACH,aAAa,CAAC,EAAE,EAAE,MAAM,GAAG,mBAAmB;IAS9C;;OAEG;IACH,GAAG,CAAC,YAAY,EAAE,MAAM,GAAG,SAAS;IASpC;;OAEG;IACH,aAAa,IAAI,4BAA4B;IAI7C;;OAEG;IACH,YAAY,CAAC,EAAE,EAAE,MAAM,EAAE,OAAO,GAAE,uBAA4B,GAAG,kBAAkB;IAiBnF;;OAEG;IACH,IAAI,IAAI,mBAAmB;IAO3B;;OAEG;IACH,GAAG,CAAC,EAAE,EAAE,MAAM,GAAG,SAAS;IAS1B;;OAEG;IACH,KAAK,IAAI,oBAAoB;IAI7B;;OAEG;IACH,IAAI,CAAC,EAAE,EAAE,MAAM,GAAG,UAAU;IAS5B;;OAEG;IACH,SAAS,IAAI,wBAAwB;IAIrC;;OAEG;IACH,QAAQ,CAAC,EAAE,EAAE,MAAM,GAAG,cAAc;IASpC;;OAEG;IACH,IAAI,CAAC,EAAE,SAA2B,GAAG,UAAU;IAS/C;;OAEG;IACH,QAAQ,IAAI,uBAAuB;IAInC;;OAEG;IACH,OAAO,CAAC,EAAE,EAAE,MAAM,GAAG,aAAa;IASlC;;OAEG;IACH,iBAAiB,IAAI,+BAA+B;IAIpD;;OAEG;IACH,eAAe,CAAC,EAAE,EAAE,MAAM,GAAG,qBAAqB;IASlD;;OAEG;IACH,KAAK,IAAI,qBAAqB;IAIxB,gBAAgB,CAAC,OAAO,EAAE,MAAM,EAAE,OAAO,CAAC,EAAE,uBAAuB,GAAG,OAAO,CAAC,IAAI,CAAC;CAU5F;AAED,MAAM,WAAW,kBAAkB;IAC/B,qCAAqC;IACrC,OAAO,CAAC,EAAE,MAAM,CAAC;IACjB,iBAAiB;IACjB,UAAU,CAAC,EAAE,MAAM,CAAC;IACpB,mBAAmB;IACnB,4BAA4B,CAAC,EAAE,MAAM,CAAC;IACtC,kBAAkB;IAClB,mBAAmB,CAAC,EAAE,0BAA0B,EAAE,CAAC;IACnD,mBAAmB;IACnB,WAAW,CAAC,EAAE,MAAM,CAAC;IACrB,KAAK,CAAC,EAAE,MAAM,CAAC;IACf,eAAe,CAAC,EAAE,MAAM,GAAG,MAAM,EAAE,CAAC;CACvC"}