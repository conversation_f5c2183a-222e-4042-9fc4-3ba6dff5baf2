/**
 * Length of id property of a Request instance in characters.
 */
export declare const REQUEST_ID_LENGTH = 15;
/**
 * Types of all emulated storages (currently used for warning messages only).
 */
export declare enum StorageTypes {
    RequestQueue = "Request queue",
    KeyValueStore = "Key-value store",
    Dataset = "Dataset"
}
/**
 * Except in dataset items, the default limit for API results is 1000.
 */
export declare const DEFAULT_API_PARAM_LIMIT = 1000;
//# sourceMappingURL=consts.d.ts.map