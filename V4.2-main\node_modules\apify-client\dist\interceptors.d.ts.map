{"version": 3, "file": "interceptors.d.ts", "sourceRoot": "", "sources": ["../src/interceptors.ts"], "names": [], "mappings": "AAAA,OAAO,KAAK,EAAE,uBAAuB,EAA2B,aAAa,EAAE,MAAM,OAAO,CAAC;AAM7F,OAAO,KAAK,EAAE,kBAAkB,EAAE,aAAa,EAAE,MAAM,eAAe,CAAC;AAGvE;;;;;;GAMG;AACH,qBAAa,wBAAyB,SAAQ,KAAK;IAC/C,IAAI,EAAE,MAAM,CAAC;IAEb,QAAQ,EAAE,aAAa,CAAC;IAEhB,KAAK,EAAE,KAAK,CAAC;gBAET,QAAQ,EAAE,aAAa,EAAE,KAAK,EAAE,KAAK;CAOpD;AAyFD,MAAM,MAAM,0BAA0B,GAAG,UAAU,CAAC,uBAAuB,CAAC,kBAAkB,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC3G,MAAM,MAAM,2BAA2B,GAAG,UAAU,CAAC,uBAAuB,CAAC,aAAa,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAEvG,eAAO,MAAM,mBAAmB,EAAE,0BAA0B,EAI3D,CAAC;AACF,eAAO,MAAM,oBAAoB,EAAE,2BAA2B,EAAwB,CAAC"}