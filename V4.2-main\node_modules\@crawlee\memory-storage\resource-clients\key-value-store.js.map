{"version": 3, "file": "key-value-store.js", "sourceRoot": "", "sources": ["../../src/resource-clients/key-value-store.ts"], "names": [], "mappings": ";;;;AAAA,6CAAyC;AACzC,+CAAsC;AACtC,yCAAoC;AACpC,6CAAuC;AAGvC,qDAAyC;AACzC,uCAAgC;AAChC,oEAA8B;AAE9B,8DAA+D;AAC/D,gDAAgD;AAChD,oDAAwE;AACxE,sCAAkE;AAElE,2DAA4E;AAE5E,oCAA8C;AAC9C,sDAAkD;AAElD,MAAM,4BAA4B,GAAG,KAAK,CAAC;AAgB3C,MAAa,mBAAoB,SAAQ,wBAAU;IAU/C,YAAY,OAAmC;QAC3C,KAAK,CAAC,OAAO,CAAC,EAAE,IAAI,IAAA,wBAAU,GAAE,CAAC,CAAC;QAVtC;;;;;WAAc;QACd;;;;mBAAY,IAAI,IAAI,EAAE;WAAC;QACvB;;;;mBAAa,IAAI,IAAI,EAAE;WAAC;QACxB;;;;mBAAa,IAAI,IAAI,EAAE;WAAC;QACxB;;;;;WAA+B;QAEd;;;;mBAAkB,IAAI,GAAG,EAAoD;WAAC;QAC9E;;;;;WAAsB;QAInC,IAAI,CAAC,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC;QACzB,IAAI,CAAC,sBAAsB,GAAG,IAAA,mBAAO,EAAC,OAAO,CAAC,oBAAoB,EAAE,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,EAAE,CAAC,CAAC;QAC1F,IAAI,CAAC,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC;IACjC,CAAC;IAED,KAAK,CAAC,GAAG;QACL,MAAM,KAAK,GAAG,MAAM,IAAA,oDAAoC,EAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,EAAE,CAAC,CAAC;QAE5F,IAAI,KAAK,EAAE,CAAC;YACR,KAAK,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC;YAC9B,OAAO,KAAK,CAAC,mBAAmB,EAAE,CAAC;QACvC,CAAC;QAED,OAAO,SAAS,CAAC;IACrB,CAAC;IAED,KAAK,CAAC,MAAM,CAAC,YAAsD,EAAE;QACjE,MAAM,MAAM,GAAG,cAAC;aACX,MAAM,CAAC;YACJ,IAAI,EAAE,cAAC,CAAC,MAAM,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC,QAAQ;SAC/C,CAAC;aACD,KAAK,CAAC,SAAS,CAAC,CAAC;QAEtB,cAAc;QACd,MAAM,iBAAiB,GAAG,MAAM,IAAA,oDAAoC,EAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,EAAE,CAAC,CAAC;QAExG,IAAI,CAAC,iBAAiB,EAAE,CAAC;YACrB,IAAI,CAAC,kBAAkB,CAAC,qBAAY,CAAC,aAAa,CAAC,CAAC;QACxD,CAAC;QAED,qBAAqB;QACrB,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC;YACf,OAAO,iBAAiB,CAAC,mBAAmB,EAAE,CAAC;QACnD,CAAC;QAED,wCAAwC;QACxC,MAAM,mBAAmB,GAAG,IAAI,CAAC,MAAM,CAAC,qBAAqB,CAAC,IAAI,CAC9D,CAAC,KAAK,EAAE,EAAE,CAAC,KAAK,CAAC,IAAI,EAAE,WAAW,EAAE,KAAK,MAAM,CAAC,IAAK,CAAC,WAAW,EAAE,CACtE,CAAC;QAEF,IAAI,mBAAmB,EAAE,CAAC;YACtB,IAAI,CAAC,qBAAqB,CAAC,qBAAY,CAAC,aAAa,EAAE,MAAM,EAAE,MAAM,CAAC,IAAI,CAAC,CAAC;QAChF,CAAC;QAED,iBAAiB,CAAC,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC;QAErC,MAAM,WAAW,GAAG,iBAAiB,CAAC,sBAAsB,CAAC;QAE7D,iBAAiB,CAAC,sBAAsB,GAAG,IAAA,mBAAO,EAC9C,IAAI,CAAC,MAAM,CAAC,uBAAuB,EACnC,MAAM,CAAC,IAAI,IAAI,iBAAiB,CAAC,IAAI,IAAI,iBAAiB,CAAC,EAAE,CAChE,CAAC;QAEF,MAAM,IAAA,eAAI,EAAC,WAAW,EAAE,iBAAiB,CAAC,sBAAsB,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC;QAEvF,oBAAoB;QACpB,iBAAiB,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC;QAEzC,OAAO,iBAAiB,CAAC,mBAAmB,EAAE,CAAC;IACnD,CAAC;IAED,KAAK,CAAC,MAAM;QACR,MAAM,UAAU,GAAG,IAAI,CAAC,MAAM,CAAC,qBAAqB,CAAC,SAAS,CAAC,CAAC,KAAK,EAAE,EAAE,CAAC,KAAK,CAAC,EAAE,KAAK,IAAI,CAAC,EAAE,CAAC,CAAC;QAEhG,IAAI,UAAU,KAAK,CAAC,CAAC,EAAE,CAAC;YACpB,MAAM,CAAC,SAAS,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,qBAAqB,CAAC,MAAM,CAAC,UAAU,EAAE,CAAC,CAAC,CAAC;YAC5E,SAAS,CAAC,eAAe,CAAC,KAAK,EAAE,CAAC;YAElC,MAAM,IAAA,aAAE,EAAC,SAAS,CAAC,sBAAsB,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC,CAAC;QACjF,CAAC;IACL,CAAC;IAED,KAAK,CAAC,QAAQ,CAAC,UAAkD,EAAE;QAC/D,MAAM,EACF,KAAK,GAAG,gCAAuB,EAC/B,iBAAiB,EACjB,MAAM,GACT,GAAG,cAAC;aACA,MAAM,CAAC;YACJ,KAAK,EAAE,cAAC,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,QAAQ;YACvC,iBAAiB,EAAE,cAAC,CAAC,MAAM,CAAC,QAAQ;YACpC,UAAU,EAAE,cAAC,CAAC,MAAM,CAAC,QAAQ,EAAE,wEAAwE;YACvG,MAAM,EAAE,cAAC,CAAC,MAAM,CAAC,QAAQ;SAC5B,CAAC;aACD,KAAK,CAAC,OAAO,CAAC,CAAC;QAEpB,cAAc;QACd,MAAM,iBAAiB,GAAG,MAAM,IAAA,oDAAoC,EAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,EAAE,CAAC,CAAC;QAExG,IAAI,CAAC,iBAAiB,EAAE,CAAC;YACrB,IAAI,CAAC,kBAAkB,CAAC,qBAAY,CAAC,aAAa,CAAC,CAAC;QACxD,CAAC;QAED,MAAM,KAAK,GAAG,EAAE,CAAC;QAEjB,KAAK,MAAM,YAAY,IAAI,iBAAiB,CAAC,eAAe,CAAC,MAAM,EAAE,EAAE,CAAC;YACpE,MAAM,MAAM,GAAG,MAAM,YAAY,CAAC,GAAG,EAAE,CAAC;YAExC,MAAM,IAAI,GAAG,MAAM,CAAC,UAAU,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;YAC7C,KAAK,CAAC,IAAI,CAAC;gBACP,GAAG,EAAE,MAAM,CAAC,GAAG;gBACf,IAAI;aACP,CAAC,CAAC;QACP,CAAC;QAED,iCAAiC;QACjC,oFAAoF;QACpF,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE;YAChB,OAAO,CAAC,CAAC,GAAG,CAAC,aAAa,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;QACtC,CAAC,CAAC,CAAC;QAEH,MAAM,aAAa,GAAG,KAAK,CAAC,MAAM,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC,MAAM,IAAI,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC;QAErF,IAAI,cAAc,GAAG,aAAa,CAAC;QACnC,IAAI,iBAAiB,EAAE,CAAC;YACpB,MAAM,MAAM,GAAG,aAAa,CAAC,SAAS,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,GAAG,KAAK,iBAAiB,CAAC,CAAC;YACjF,IAAI,MAAM,KAAK,CAAC,CAAC;gBAAE,cAAc,GAAG,aAAa,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;QACxE,CAAC;QAED,MAAM,YAAY,GAAG,cAAc,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC;QAEpD,MAAM,eAAe,GAAG,aAAa,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QAC7C,MAAM,gBAAgB,GAAG,YAAY,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QAC7C,MAAM,gCAAgC,GAAG,eAAe,KAAK,gBAAgB,CAAC;QAC9E,MAAM,qBAAqB,GAAG,gCAAgC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,gBAAgB,EAAE,GAAG,CAAC;QAEnG,iBAAiB,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC;QAE1C,OAAO;YACH,KAAK,EAAE,YAAY,CAAC,MAAM;YAC1B,KAAK;YACL,iBAAiB;YACjB,WAAW,EAAE,CAAC,gCAAgC;YAC9C,qBAAqB;YACrB,KAAK,EAAE,YAAY;SACtB,CAAC;IACN,CAAC;IAED;;;;;OAKG;IACH,KAAK,CAAC,YAAY,CAAC,GAAW;QAC1B,cAAC,CAAC,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QAEpB,cAAc;QACd,MAAM,iBAAiB,GAAG,MAAM,IAAA,oDAAoC,EAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,EAAE,CAAC,CAAC;QAExG,IAAI,CAAC,iBAAiB,EAAE,CAAC;YACrB,IAAI,CAAC,kBAAkB,CAAC,qBAAY,CAAC,aAAa,CAAC,CAAC;QACxD,CAAC;QAED,OAAO,iBAAiB,CAAC,eAAe,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;IACtD,CAAC;IAED,KAAK,CAAC,SAAS,CACX,GAAW,EACX,UAAuD,EAAE;QAEzD,cAAC,CAAC,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QACpB,cAAC,CAAC,MAAM,CAAC;YACL,MAAM,EAAE,cAAC,CAAC,OAAO,CAAC,QAAQ;YAC1B,2CAA2C;YAC3C,8CAA8C;YAC9C,MAAM,EAAE,cAAC,CAAC,OAAO,CAAC,QAAQ;YAC1B,eAAe,EAAE,cAAC,CAAC,OAAO,CAAC,QAAQ;SACtC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;QAElB,cAAc;QACd,MAAM,iBAAiB,GAAG,MAAM,IAAA,oDAAoC,EAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,EAAE,CAAC,CAAC;QAExG,IAAI,CAAC,iBAAiB,EAAE,CAAC;YACrB,IAAI,CAAC,kBAAkB,CAAC,qBAAY,CAAC,aAAa,CAAC,CAAC;QACxD,CAAC;QAED,MAAM,YAAY,GAAG,iBAAiB,CAAC,eAAe,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;QAEhE,IAAI,CAAC,YAAY,EAAE,CAAC;YAChB,OAAO,SAAS,CAAC;QACrB,CAAC;QAED,MAAM,KAAK,GAAG,MAAM,YAAY,CAAC,GAAG,EAAE,CAAC;QAEvC,MAAM,MAAM,GAAgC;YACxC,GAAG,EAAE,KAAK,CAAC,GAAG;YACd,KAAK,EAAE,KAAK,CAAC,KAAK;YAClB,WAAW,EAAE,KAAK,CAAC,WAAW,IAAK,oBAAI,CAAC,WAAW,CAAC,KAAK,CAAC,SAAS,CAAY;SAClF,CAAC;QAEF,IAAI,OAAO,CAAC,MAAM,EAAE,CAAC;YACjB,MAAM,CAAC,KAAK,GAAG,sBAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;QAC/C,CAAC;aAAM,IAAI,OAAO,CAAC,MAAM,EAAE,CAAC;YACxB,MAAM,CAAC,KAAK,GAAG,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;QAC7C,CAAC;aAAM,CAAC;YACJ,MAAM,CAAC,KAAK,GAAG,IAAA,4BAAc,EAAC,MAAM,CAAC,KAAK,EAAE,MAAM,CAAC,WAAY,CAAC,CAAC;QACrE,CAAC;QAED,iBAAiB,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC;QAE1C,OAAO,MAAM,CAAC;IAClB,CAAC;IAED,KAAK,CAAC,SAAS,CAAC,MAAmC;QAC/C,cAAC,CAAC,MAAM,CAAC;YACL,GAAG,EAAE,cAAC,CAAC,MAAM,CAAC,iBAAiB,CAAC,CAAC,CAAC;YAClC,KAAK,EAAE,cAAC,CAAC,KAAK,CACV,cAAC,CAAC,IAAI,EACN,cAAC,CAAC,MAAM,EACR,cAAC,CAAC,MAAM,EACR,cAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,EAClB,cAAC,CAAC,QAAQ,CAAC,WAAW,CAAC,EACvB,cAAC,CAAC,UAAU,EAAE;YACd,iHAAiH;YACjH,cAAC;iBACI,MAAM,CAAC,EAAE,CAAC;iBACV,oBAAoB,CAAC,KAAK,CAAC,CACnC;YACD,WAAW,EAAE,cAAC,CAAC,MAAM,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC,QAAQ;SACtD,CAAC,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;QAEjB,cAAc;QACd,MAAM,iBAAiB,GAAG,MAAM,IAAA,oDAAoC,EAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,EAAE,CAAC,CAAC;QAExG,IAAI,CAAC,iBAAiB,EAAE,CAAC;YACrB,IAAI,CAAC,kBAAkB,CAAC,qBAAY,CAAC,aAAa,CAAC,CAAC;QACxD,CAAC;QAED,MAAM,EAAE,GAAG,EAAE,GAAG,MAAM,CAAC;QACvB,IAAI,EAAE,KAAK,EAAE,WAAW,EAAE,GAAG,MAAM,CAAC;QAEpC,MAAM,aAAa,GAAG,IAAA,gBAAQ,EAAC,KAAK,CAAC,CAAC;QAEtC,MAAM,qBAAqB,GAAG,aAAa,IAAI,IAAA,gBAAQ,EAAC,KAAK,CAAC,CAAC;QAC/D,iEAAiE;QACjE,IAAI,CAAC,WAAW,EAAE,CAAC;YACf,IAAI,qBAAqB;gBAAE,WAAW,GAAG,0BAA0B,CAAC;iBAC/D,IAAI,OAAO,KAAK,KAAK,QAAQ;gBAAE,WAAW,GAAG,2BAA2B,CAAC;;gBACzE,WAAW,GAAG,iCAAiC,CAAC;QACzD,CAAC;QAED,MAAM,SAAS,GAAG,oBAAI,CAAC,SAAS,CAAC,WAAW,CAAC,IAAI,4BAA4B,CAAC;QAE9E,MAAM,iBAAiB,GAAG,SAAS,KAAK,MAAM,CAAC;QAE/C,IAAI,iBAAiB,IAAI,CAAC,qBAAqB,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE,CAAC;YAC3E,IAAI,CAAC;gBACD,KAAK,GAAG,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;YAC3C,CAAC;YAAC,OAAO,GAAQ,EAAE,CAAC;gBAChB,MAAM,GAAG,GAAG,8FAA8F,GAAG,CAAC,OAAO,EAAE,CAAC;gBACxH,MAAM,IAAI,KAAK,CAAC,GAAG,CAAC,CAAC;YACzB,CAAC;QACL,CAAC;QAED,IAAI,aAAa,EAAE,CAAC;YAChB,MAAM,MAAM,GAAG,EAAE,CAAC;YAClB,IAAI,KAAK,EAAE,MAAM,KAAK,IAAI,KAAK,EAAE,CAAC;gBAC9B,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACvB,CAAC;YACD,KAAK,GAAG,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;QAClC,CAAC;QAED,MAAM,OAAO,GAAG;YACZ,SAAS;YACT,GAAG;YACH,KAAK;YACL,WAAW;SACc,CAAC;QAE9B,MAAM,KAAK,GAAG,IAAA,qDAAmC,EAAC;YAC9C,cAAc,EAAE,IAAI,CAAC,MAAM,CAAC,cAAc;YAC1C,cAAc,EAAE,iBAAiB,CAAC,sBAAsB;YACxD,aAAa,EAAE,iBAAiB,CAAC,MAAM,CAAC,aAAa;SACxD,CAAC,CAAC;QAEH,MAAM,KAAK,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;QAE5B,iBAAiB,CAAC,eAAe,CAAC,GAAG,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;QAElD,iBAAiB,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC;IAC7C,CAAC;IAED,KAAK,CAAC,YAAY,CAAC,GAAW;QAC1B,cAAC,CAAC,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QAEpB,cAAc;QACd,MAAM,iBAAiB,GAAG,MAAM,IAAA,oDAAoC,EAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,EAAE,CAAC,CAAC;QAExG,IAAI,CAAC,iBAAiB,EAAE,CAAC;YACrB,IAAI,CAAC,kBAAkB,CAAC,qBAAY,CAAC,aAAa,CAAC,CAAC;QACxD,CAAC;QAED,MAAM,KAAK,GAAG,iBAAiB,CAAC,eAAe,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;QAEzD,IAAI,KAAK,EAAE,CAAC;YACR,iBAAiB,CAAC,eAAe,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;YAC9C,iBAAiB,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC;YACzC,MAAM,KAAK,CAAC,MAAM,EAAE,CAAC;QACzB,CAAC;IACL,CAAC;IAED,mBAAmB;QACf,OAAO;YACH,EAAE,EAAE,IAAI,CAAC,EAAE;YACX,IAAI,EAAE,IAAI,CAAC,IAAI;YACf,UAAU,EAAE,IAAI,CAAC,UAAU;YAC3B,SAAS,EAAE,IAAI,CAAC,SAAS;YACzB,UAAU,EAAE,IAAI,CAAC,UAAU;YAC3B,MAAM,EAAE,GAAG;SACd,CAAC;IACN,CAAC;IAEO,gBAAgB,CAAC,eAAwB;QAC7C,IAAI,CAAC,UAAU,GAAG,IAAI,IAAI,EAAE,CAAC;QAE7B,IAAI,eAAe,EAAE,CAAC;YAClB,IAAI,CAAC,UAAU,GAAG,IAAI,IAAI,EAAE,CAAC;QACjC,CAAC;QAED,MAAM,IAAI,GAAG,IAAI,CAAC,mBAAmB,EAAE,CAAC;QACxC,IAAA,2CAAsB,EAAC;YACnB,MAAM,EAAE,iBAAiB;YACzB,IAAI;YACJ,UAAU,EAAE,gBAAgB;YAC5B,eAAe,EAAE,IAAI,CAAC,sBAAsB;YAC5C,EAAE,EAAE,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,EAAE;YACxB,aAAa,EAAE,IAAI,CAAC,MAAM,CAAC,aAAa;YACxC,cAAc,EAAE,IAAI,CAAC,MAAM,CAAC,cAAc;SAC7C,CAAC,CAAC;IACP,CAAC;CACJ;AAvVD,kDAuVC"}