import { PrismaClient } from '@prisma/client';
import PricingService from '../src/services/pricing.service.js';

const prisma = new PrismaClient();
const pricingService = new PricingService();

/**
 * Migration script to resolve Prisma conflicts and update pricing structure
 * From $30 per 10K to $20 per 10K per month with 1-month credit expiration
 */
async function migratePricing() {
  console.log('🚀 Starting pricing migration...');

  try {
    // Step 1: Handle potential Prisma conflicts
    await handlePrismaConflicts();

    // Step 2: Update pricing structure
    await updatePricingStructure();

    // Step 3: Migrate existing users
    await migrateExistingUsers();

    // Step 4: Set up cleanup job
    await setupCleanupJob();

    console.log('✅ Pricing migration completed successfully!');
  } catch (error) {
    console.error('❌ Migration failed:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

/**
 * Handle common Prisma conflicts
 */
async function handlePrismaConflicts() {
  console.log('🔧 Resolving Prisma conflicts...');

  try {
    // Reset Prisma client connection
    await prisma.$disconnect();
    await prisma.$connect();

    // Check for schema conflicts and resolve them
    await prisma.$executeRaw`SELECT 1`; // Test connection

    // Drop and recreate indexes if they exist with conflicts
    try {
      await prisma.$executeRaw`DROP INDEX IF EXISTS "users_email_key"`;
      await prisma.$executeRaw`CREATE UNIQUE INDEX "users_email_key" ON "users"("email")`;
    } catch (error) {
      console.log('Index already exists or not needed');
    }

    console.log('✅ Prisma conflicts resolved');
  } catch (error) {
    console.error('Error resolving Prisma conflicts:', error);
    throw error;
  }
}

/**
 * Update pricing structure from $30 to $20 per 10K
 */
async function updatePricingStructure() {
  console.log('💰 Updating pricing structure...');

  try {
    // Create new pricing plan with updated rates
    const newPlan = await prisma.subscriptionPlan.upsert({
      where: { name: 'Standard Plan' },
      update: {
        pricePerUnit: 20.00, // Updated from $30 to $20
        creditsPerUnit: 10000,
        billingCycle: 'MONTHLY',
        creditExpiryDays: 30, // Credits expire in 1 month
        description: 'Email verification and processing - $20 per 10K credits per month'
      },
      create: {
        name: 'Standard Plan',
        description: 'Email verification and processing - $20 per 10K credits per month',
        pricePerUnit: 20.00,
        creditsPerUnit: 10000,
        billingCycle: 'MONTHLY',
        creditExpiryDays: 30
      }
    });

    // Deactivate old pricing plans if they exist
    await prisma.subscriptionPlan.updateMany({
      where: {
        pricePerUnit: 30.00, // Old pricing
        isActive: true
      },
      data: {
        isActive: false
      }
    });

    console.log('✅ Pricing structure updated:', newPlan);
    return newPlan;
  } catch (error) {
    console.error('Error updating pricing structure:', error);
    throw error;
  }
}

/**
 * Migrate existing users to new pricing structure
 */
async function migrateExistingUsers() {
  console.log('👥 Migrating existing users...');

  try {
    // Get all existing subscriptions
    const existingSubscriptions = await prisma.subscription.findMany({
      include: { user: true, plan: true }
    });

    console.log(`Found ${existingSubscriptions.length} existing subscriptions`);

    // Get the new standard plan
    const newPlan = await prisma.subscriptionPlan.findFirst({
      where: { 
        name: 'Standard Plan',
        isActive: true 
      }
    });

    if (!newPlan) {
      throw new Error('New pricing plan not found');
    }

    for (const subscription of existingSubscriptions) {
      try {
        // Calculate remaining time in current period
        const now = new Date();
        const timeRemaining = subscription.currentPeriodEnd - now;
        
        // If subscription is still active and has time remaining
        if (timeRemaining > 0 && subscription.status === 'ACTIVE') {
          // Update to new plan but keep existing credits and period
          await prisma.subscription.update({
            where: { id: subscription.id },
            data: {
              planId: newPlan.id
            }
          });

          console.log(`✅ Migrated user ${subscription.userId} to new plan`);
        } else {
          // Expired subscription - expire it properly
          await pricingService.expireSubscription(subscription.userId);
          console.log(`⏰ Expired subscription for user ${subscription.userId}`);
        }
      } catch (error) {
        console.error(`Error migrating user ${subscription.userId}:`, error);
      }
    }

    console.log('✅ User migration completed');
  } catch (error) {
    console.error('Error migrating existing users:', error);
    throw error;
  }
}

/**
 * Set up cleanup job for expired credits
 */
async function setupCleanupJob() {
  console.log('🧹 Setting up cleanup job...');

  try {
    // Run initial cleanup
    const cleanedUp = await pricingService.cleanupExpiredSubscriptions();
    console.log(`✅ Initial cleanup completed: ${cleanedUp} subscriptions processed`);

    // Note: In production, you would set up a cron job here
    console.log('📝 Note: Set up a cron job to run pricingService.cleanupExpiredSubscriptions() daily');
  } catch (error) {
    console.error('Error setting up cleanup job:', error);
    throw error;
  }
}

/**
 * Rollback function in case of issues
 */
async function rollbackMigration() {
  console.log('🔄 Rolling back migration...');

  try {
    // Reactivate old pricing plans
    await prisma.subscriptionPlan.updateMany({
      where: {
        pricePerUnit: 30.00,
        isActive: false
      },
      data: {
        isActive: true
      }
    });

    // Deactivate new pricing plan
    await prisma.subscriptionPlan.updateMany({
      where: {
        pricePerUnit: 20.00,
        isActive: true
      },
      data: {
        isActive: false
      }
    });

    console.log('✅ Migration rolled back successfully');
  } catch (error) {
    console.error('Error rolling back migration:', error);
    throw error;
  }
}

// Run migration if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
  migratePricing()
    .then(() => {
      console.log('🎉 Migration completed successfully!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 Migration failed:', error);
      process.exit(1);
    });
}

export { migratePricing, rollbackMigration };
