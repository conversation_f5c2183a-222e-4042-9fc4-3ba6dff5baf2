import type { ProxyConfigurationOptions as CoreProxyConfigurationOptions, ProxyInfo as CoreProxyInfo } from '@crawlee/core';
import { ProxyConfiguration as CoreProxyConfiguration } from '@crawlee/core';
import { Configuration } from './configuration.js';
export interface ProxyConfigurationOptions extends CoreProxyConfigurationOptions {
    /**
     * User's password for the proxy. By default, it is taken from the `APIFY_PROXY_PASSWORD`
     * environment variable, which is automatically set by the system when running the Actors.
     */
    password?: string;
    /**
     * An array of proxy groups to be used by the [Apify Proxy](https://docs.apify.com/proxy).
     * If not provided, the proxy will select the groups automatically.
     */
    groups?: string[];
    /**
     * If set and relevant proxies are available in your Apify account, all proxied requests will
     * use IP addresses that are geolocated to the specified country. For example `GB` for IPs
     * from Great Britain. Note that online services often have their own rules for handling
     * geolocation and thus the country selection is a best attempt at geolocation, rather than
     * a guaranteed hit. This parameter is optional, by default, each proxied request is assigned
     * an IP address from a random country. The country code needs to be a two letter ISO country code. See the
     * [full list of available country codes](https://en.wikipedia.org/wiki/ISO_3166-1_alpha-2#Officially_assigned_code_elements).
     * This parameter is optional, by default, the proxy uses all available proxy servers from all countries.
     * on the Apify cloud, or when using the [Apify CLI](https://github.com/apify/apify-cli).
     */
    countryCode?: string;
    /**
     * Same option as `groups` which can be used to
     * configurate the proxy by UI input schema. You should use the `groups` option in your crawler code.
     */
    apifyProxyGroups?: string[];
    /**
     * Same option as `countryCode` which can be used to
     * configurate the proxy by UI input schema. You should use the `countryCode` option in your crawler code.
     */
    apifyProxyCountry?: string;
    /**
     * Multiple different ProxyConfigurationOptions stratified into tiers. Crawlee crawlers will switch between those tiers
     * based on the blocked request statistics.
     */
    tieredProxyConfig?: Omit<ProxyConfigurationOptions, keyof CoreProxyConfigurationOptions | 'tieredProxyConfig'>[];
}
/**
 * The main purpose of the ProxyInfo object is to provide information
 * about the current proxy connection used by the crawler for the request.
 * Outside of crawlers, you can get this object by calling {@link ProxyConfiguration.newProxyInfo}.
 *
 * **Example usage:**
 *
 * ```javascript
 *
 * const proxyConfiguration = await Actor.createProxyConfiguration({
 *   groups: ['GROUP1', 'GROUP2'] // List of Apify Proxy groups
 *   countryCode: 'US',
 * });
 *
 * // Getting proxyInfo object by calling class method directly
 * const proxyInfo = proxyConfiguration.newProxyInfo();
 *
 * // In crawler
 * const crawler = new CheerioCrawler({
 *   // ...
 *   proxyConfiguration,
 *   requestHandler({ proxyInfo }) {
 *       // Getting used proxy URL
 *       const proxyUrl = proxyInfo.url;
 *
 *       // Getting ID of used Session
 *       const sessionIdentifier = proxyInfo.sessionId;
 *   }
 * })
 *
 * ```
 */
export interface ProxyInfo extends CoreProxyInfo {
    /**
     * An array of proxy groups to be used by the [Apify Proxy](https://docs.apify.com/proxy).
     * If not provided, the proxy will select the groups automatically.
     */
    groups: string[];
    /**
     * If set and relevant proxies are available in your Apify account, all proxied requests will
     * use IP addresses that are geolocated to the specified country. For example `GB` for IPs
     * from Great Britain. Note that online services often have their own rules for handling
     * geolocation and thus the country selection is a best attempt at geolocation, rather than
     * a guaranteed hit. This parameter is optional, by default, each proxied request is assigned
     * an IP address from a random country. The country code needs to be a two letter ISO country code. See the
     * [full list of available country codes](https://en.wikipedia.org/wiki/ISO_3166-1_alpha-2#Officially_assigned_code_elements).
     * This parameter is optional, by default, the proxy uses all available proxy servers from all countries.
     */
    countryCode?: string;
    /**
     * User's password for the proxy. By default, it is taken from the `APIFY_PROXY_PASSWORD`
     * environment variable, which is automatically set by the system when running the Actors
     * on the Apify cloud, or when using the [Apify CLI](https://github.com/apify/apify-cli).
     */
    password: string;
}
/**
 * Configures connection to a proxy server with the provided options. Proxy servers are used to prevent target websites from blocking
 * your crawlers based on IP address rate limits or blacklists. Setting proxy configuration in your crawlers automatically configures
 * them to use the selected proxies for all connections. You can get information about the currently used proxy by inspecting
 * the {@link ProxyInfo} property in your crawler's page function. There, you can inspect the proxy's URL and other attributes.
 *
 * The proxy servers are managed by [Apify Proxy](https://docs.apify.com/proxy). To be able to use Apify Proxy,
 * you need an Apify account and access to the selected proxies. If you provide no configuration option,
 * the proxies will be managed automatically using a smart algorithm.
 *
 * If you want to use your own proxies, use the {@link ProxyConfigurationOptions.proxyUrls} option. Your list of proxy URLs will
 * be rotated by the configuration if this option is provided.
 *
 * **Example usage:**
 *
 * ```javascript
 *
 * const proxyConfiguration = await Actor.createProxyConfiguration({
 *   groups: ['GROUP1', 'GROUP2'] // List of Apify Proxy groups
 *   countryCode: 'US',
 * });
 *
 * const crawler = new CheerioCrawler({
 *   // ...
 *   proxyConfiguration,
 *   requestHandler({ proxyInfo }) {
 *      const usedProxyUrl = proxyInfo.url; // Getting the proxy URL
 *   }
 * })
 *
 * ```
 * @category Scaling
 */
export declare class ProxyConfiguration extends CoreProxyConfiguration {
    readonly config: Configuration;
    private groups;
    private countryCode?;
    private password?;
    private hostname;
    private port?;
    private usesApifyProxy?;
    /**
     * @internal
     */
    constructor(options?: ProxyConfigurationOptions, config?: Configuration);
    /**
     * Loads proxy password if token is provided and checks access to Apify Proxy and provided proxy groups
     * if Apify Proxy configuration is used.
     * Also checks if country has access to Apify Proxy groups if the country code is provided.
     *
     * You should use the {@link createProxyConfiguration} function to create a pre-initialized
     * `ProxyConfiguration` instance instead of calling this manually.
     */
    initialize(): Promise<boolean>;
    /**
     * This function creates a new {@link ProxyInfo} info object.
     * It is used by CheerioCrawler and PuppeteerCrawler to generate proxy URLs and also to allow the user to inspect
     * the currently used proxy via the requestHandler parameter `proxyInfo`.
     * Use it if you want to work with a rich representation of a proxy URL.
     * If you need the URL string only, use {@link ProxyConfiguration.newUrl}.
     * @param [sessionId]
     *  Represents the identifier of user {@link Session} that can be managed by the {@link SessionPool} or
     *  you can use the Apify Proxy [Session](https://docs.apify.com/proxy#sessions) identifier.
     *  When the provided sessionId is a number, it's converted to a string. Property sessionId of
     *  {@link ProxyInfo} is always returned as a type string.
     *
     *  All the HTTP requests going through the proxy with the same session identifier
     *  will use the same target proxy server (i.e. the same IP address).
     *  The identifier must not be longer than 50 characters and include only the following: `0-9`, `a-z`, `A-Z`, `"."`, `"_"` and `"~"`.
     * @return Represents information about used proxy and its configuration.
     */
    newProxyInfo(sessionId?: string | number, options?: Parameters<CoreProxyConfiguration['newProxyInfo']>[1]): Promise<ProxyInfo | undefined>;
    /**
     * Returns a new proxy URL based on provided configuration options and the `sessionId` parameter.
     * @param [sessionId]
     *  Represents the identifier of user {@link Session} that can be managed by the {@link SessionPool} or
     *  you can use the Apify Proxy [Session](https://docs.apify.com/proxy#sessions) identifier.
     *  When the provided sessionId is a number, it's converted to a string.
     *
     *  All the HTTP requests going through the proxy with the same session identifier
     *  will use the same target proxy server (i.e. the same IP address).
     *  The identifier must not be longer than 50 characters and include only the following: `0-9`, `a-z`, `A-Z`, `"."`, `"_"` and `"~"`.
     * @return A string with a proxy URL, including authentication credentials and port number.
     *  For example, `http://bob:<EMAIL>:8000`
     */
    newUrl(sessionId?: string | number, options?: Parameters<CoreProxyConfiguration['newUrl']>[1]): Promise<string | undefined>;
    protected _generateTieredProxyUrls(tieredProxyConfig: NonNullable<ProxyConfigurationOptions['tieredProxyConfig']>, globalOptions: ProxyConfigurationOptions): string[][];
    /**
     * Returns proxy username.
     */
    protected _getUsername(sessionId?: string): string;
    protected composeDefaultUrl(sessionId?: string): string;
    /**
     * Fetch & set the proxy password from Apify API if an Apify token is provided.
     */
    protected _setPasswordIfToken(): Promise<void>;
    /**
     * Checks whether the user has access to the proxies specified in the provided ProxyConfigurationOptions.
     * If the check can not be made, it only prints a warning and allows the program to continue. This is to
     * prevent program crashes caused by short downtimes of Proxy.
     */
    protected _checkAccess(): Promise<boolean>;
    /**
     * Apify Proxy can be down for a second or a minute, but this should not crash processes.
     */
    protected _fetchStatus(): Promise<{
        connected: boolean;
        connectionError: string;
        isManInTheMiddle: boolean;
    } | undefined>;
    /**
     * Throws cannot combine custom proxies with Apify Proxy
     * @internal
     */
    protected _throwCannotCombineCustomWithApify(): void;
}
//# sourceMappingURL=proxy_configuration.d.ts.map